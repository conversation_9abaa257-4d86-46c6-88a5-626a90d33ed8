spring:
  application:
    name: hs-personal-journey
  config:
    use-legacy-processing: true
  main:
    allow-bean-definition-overriding: true

server:
  port: 8892

  servlet:
    context-path: /v3/journey

webclient:
  http:
    connect-timeout: 10s
    read-timeout: 30s
    write-timeout: 30s
    max-connections: 50
    pending-acquire-max-count: 1000

camunda:
  bpm:
    enabled: false
    job-execution:
      enabled: false
    database:
      schema-update: none
    jpa:
      enabled: false

---
spring:
  profiles: dev
  profiles.include: postgres-db, b2b, shedlock-jdbc
  flyway:
    enabled: false
    locations: classpath:database/db/migration
  jpa:
    properties:
      hibernate:
        show_sql: true
        id:
          new_generator_mappings: true
        current_session_context_class: thread
        temp:
          use_jdbc_metadata_defaults: false
    hibernate:
      ddl-auto: validate

management:
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: "health,info,metrics,prometheus"
  health:
    camunda:
      enabled: false

security:
  oauth2:
    client:
      client-id: hs-platform
      client-secret: uAcbVei2Nnj6tmQh2g7B4LOaqZwuRj7v
      scope: openid,profile,email,roles
      oauth-base-url: http://localhost:8080/v3-auth/realms/vhs/protocol/openid-connect/auth
    resource:
      id: hs-platform
      token-info-uri: http://localhost:8080/v3-auth/realms/vhs/protocol/openid-connect/token
      user-info-uri: http://localhost:8080/v3-auth/realms/vhs/protocol/openid-connect/userinfo
      jwk:
        key-set-uri: http://localhost:8080/v3-auth/realms/vhs/protocol/openid-connect/certs

hs:
  oauth2:
    url: http://localhost:8080/v3-auth/realms/vhs/protocol/openid-connect/token
    client:
      id: backend-to-backend-client
      secret: cjL9jRlVMh8edhWdbe090VBqZ9LJlSAT

platform:
  logging:
    console-level: "info"
    json-console-level: "off"
    flat-file-level: "off"
    json-file-level: "off"
  oauth2:
    resourceserver:
      cors:
        allowed-origins: '*'
        allowed-methods: GET,HEAD,POST,PUT,PATCH,DELETE,OPTIONS,TRACE
        allowed-headers: X-XSRF-TOKEN,XSRF-TOKEN,CONTENT-TYPE
        allow-credentials: true
        max-age: 10
      developer-endpoints:
        - paths: /actuator/**,/**/actuator/**
          role: DEVELOPER
      public-endpoints:
        - paths: /**

scheduler:
  mid-point-reminder-cron: "0 0 6 * * *"

integration:
  pac-man:
    url: http://localhost:33666/v3/program
  config-flag:
    url: http://localhost:34567/v3/config-flag-aggregator
  vap:
    url: http://vgtweblogic04.discsrv.co.za:7002/vap
  group-coaching:
    url: http://localhost:7575/v3/group-coaching
---
spring:
  profiles: local-test
  profiles.include: hana-db, b2b, shedlock-jdbc, kafka
  jpa:
    properties:
      hibernate:
        id:
          new_generator_mappings: true
        current_session_context_class: thread
        temp:
          use_jdbc_metadata_defaults: false
    hibernate:
      ddl-auto: validate
  kafka:
    bootstrap-servers: localhost:9092

security:
  oauth2:
    client:
      client-id: hs-platform
      client-secret: uAcbVei2Nnj6tmQh2g7B4LOaqZwuRj7v
      scope: openid,profile,email,roles
      oauth-base-url: https://integrationtest.powerofvitality.com/v3-auth/realms/vhs/protocol/openid-connect/auth
    resource:
      id: hs-platform
      token-info-uri: https://integrationtest.powerofvitality.com/v3-auth/realms/vhs/protocol/openid-connect/token
      user-info-uri: https://integrationtest.powerofvitality.com/v3-auth/realms/vhs/protocol/openid-connect/userinfo
      jwk:
        key-set-uri: https://integrationtest.powerofvitality.com/v3-auth/realms/vhs/protocol/openid-connect/certs

hs:
  oauth2:
    url: https://integrationtest.powerofvitality.com/v3-auth/realms/vhs/protocol/openid-connect/token
    client:
      id: backend-to-backend-client
      secret: cjL9jRlVMh8edhWdbe090VBqZ9LJlSAT

platform:
  logging:
    console-level: "info"
    json-console-level: "off"
    flat-file-level: "off"
    json-file-level: "off"
  oauth2:
    resourceserver:
      cors:
        allowed-origins: '*'
        allowed-methods: GET,HEAD,POST,PUT,PATCH,DELETE,OPTIONS,TRACE
        allowed-headers: X-XSRF-TOKEN,XSRF-TOKEN,CONTENT-TYPE
        allow-credentials: true
        max-age: 10
      developer-endpoints:
        - paths: /actuator/**,/**/actuator/**
          role: DEVELOPER
      public-endpoints:
        - paths: /**

scheduler:
  mid-point-reminder-cron: "-"
  journey-reward:
    cron: "-"

integration:
  pac-man:
    url: http://hs-pac-man:33666/v3/program
  config-flag:
    url: http://hs-config-flag-aggregator:34567/v3/config-flag-aggregator
  vap:
    url: http://vgtweblogic04.discsrv.co.za:7002/vap
  group-coaching:
    url: http://hs-entity-data-service:7575/v3/group-coaching
---
spring:
  profiles: kafka
  kafka:
    consumer:
      auto-offset-reset: earliest
      group-id: hs-personal-journey-group
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: "*"
    admin:
      client-id: admin-personal-journey-client-id
    bootstrap-servers: localhost:29092

---
spring:
  profiles: h2-db
  flyway:
    enabled: false

    locations: classpath:/database/db/migration
  datasource:
    type: org.apache.tomcat.jdbc.pool.DataSource
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:JOURNEY
    username: sa
    password:
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
---
spring:
  profiles: db-cache
  jpa:
    properties:
      javax:
        persistence:
          sharedCache:
            mode: ENABLE_SELECTIVE
      hibernate:
        generate_statistics: true
        cache:
          missing_cache_strategy: create
          use_second_level_cache: true
          region:
            factory_class: org.hibernate.cache.ehcache.EhCacheRegionFactory

db-cache:
  maxEntriesLocalHeap: 1000
  memoryStoreEvictionPolicy: LRU
  name: journeyCache
---
spring:
  profiles: postgres-db
  flyway:
    enabled: true
    locations: classpath:/database/db/migration
  datasource:
    type: org.apache.tomcat.jdbc.pool.DataSource
    driver-class-name: org.postgresql.Driver
    url: ****************************************
    username: postgres
    password: postgres
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: validate
---
spring:
  profiles: hana-db
  flyway:
    enabled: false
  datasource:
    driver-class-name: com.sap.db.jdbc.Driver
    url: ${HANA_DB_URL:**************************}
    username: ${HANA_DB_USERNAME:SVR_VITALITY_DATA}
    password: ${HANA_DB_PASSWORD:password}
    type: org.apache.tomcat.jdbc.pool.DataSource
    tomcat:
      initial-size: 10
      min-idle: 10
      test-on-borrow: true
      test-while-idle: true
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.HANAColumnStoreDialect
---
logging:
  level:
    org.apache.kafka: ERROR
#    org.springframework.transaction: DEBUG
#    org.springframework.transaction.interceptor: TRACE
#    org.springframework.transaction.support: TRACE
#    org.hibernate.transaction: DEBUG
#    org.hibernate.engine.transaction.internal.TransactionImpl: DEBUG
#    org.hibernate.SQL: DEBUG
#    org.hibernate.type.descriptor.sql: TRACE
