spring:
  application:
    name: hs-journey-importer
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  profiles:
    group:
      local-h2: local,h2-db,data-generator
  batch:
    job:
      enabled: false
    jdbc:
      table-prefix: JOURNEY_IMPORT.BATCH_
      initialize-schema: never
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

server:
  port: 35350
  servlet:
    context-path: /v3/journey-importer

springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    try-it-out-enabled: true

batch:
  thread-pool-size: 4
  dpp:
    staging:
      chunk-size: 200
      concurrent: false
    normalize:
      chunk-size: 10
      concurrent: false
    enrich:
      chunk-size: 100
      concurrent: true
    enroll:
      chunk-size: 5
      concurrent: true

entity-service:
  connect-timeout: 3s
  read-timeout: 10s
  retry-attempts: 3

personal-journey-service:
  connect-timeout: 3s
  read-timeout: 10s
  retry-attempts: 3

---
spring:
  config:
    activate:
      on-profile: local
  cloud:
    config:
      import-check:
        enabled: false
  jpa:
    properties:
      generate_statistics: true
      hibernate:
        id:
          new_generator_mappings: true
        current_session_context_class: thread
        temp:
          use_jdbc_metadata_defaults: false
    hibernate:
      ddl-auto: none
  autoconfigure:
    exclude: org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration

springdoc:
  show-actuator: false

entity-service:
  url: http://hs-entity-data-service:32000/v3/entity

personal-journey-service:
  url: http://localhost:8892/v3/journey

logging:
  pattern:
    level: "%5p [${spring.application.name:},%X{trace_id:-},%X{span_id:-}]"
  level:
    root: INFO
#    com.vitality.journey.importer.service.imprt.staging: DEBUG
#    com.vitality.journey.importer.service.imprt.normalize: DEBUG
#    org.springframework.transaction: DEBUG
#    org.springframework.transaction.interceptor: DEBUG
#    org.springframework.orm.jpa: DEBUG
#    org.hibernate.engine.transaction.internal.TransactionImpl: DEBUG
#    org.hibernate.engine.transaction.spi: DEBUG
#    org.hibernate.SQL: DEBUG
#    org.hibernate.orm.jdbc.bind: TRACE
#    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

management:
  metrics:
    tags:
      application: ${spring.application.name}
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: "health,info,metrics"

security:
  oauth2:
    client:
      client-id: hs-platform
      client-secret: iCqganMJ2jZLcrcBtt2pbkQO7RuFh3G6
      scope: openid,profile,email,roles
      oauth-base-url: http://k8s-d2hpv1fu-gatewayk-d572bfa8b9-**********.us-east-1.elb.amazonaws.com/auth/realms/vhs/protocol/openid-connect/auth
    resource:
      id: hs-platform
      token-info-uri: http://k8s-d2hpv1fu-gatewayk-d572bfa8b9-**********.us-east-1.elb.amazonaws.com/auth/realms/vhs/protocol/openid-connect/token
      user-info-uri: http://k8s-d2hpv1fu-gatewayk-d572bfa8b9-**********.us-east-1.elb.amazonaws.com/auth/realms/vhs/protocol/openid-connect/userinfo
      jwk:
        key-set-uri: http://k8s-d2hpv1fu-gatewayk-d572bfa8b9-**********.us-east-1.elb.amazonaws.com/auth/realms/vhs/protocol/openid-connect/certs

hs:
  oauth2:
    url: https://integrationtest.powerofvitality.com/v3-auth/realms/vhs/protocol/openid-connect/token
    client:
      id: backend-to-backend-client
      secret: cjL9jRlVMh8edhWdbe090VBqZ9LJlSAT

platform:
  logging:
    console-level: "info"
    json-console-level: "off"
    flat-file-level: "off"
    json-file-level: "off"
  oauth2:
    resourceserver:
      cors:
        allowed-origins: '*'
        allowed-methods: GET,HEAD,POST,PUT,PATCH,DELETE,OPTIONS,TRACE
        allowed-headers: X-XSRF-TOKEN,XSRF-TOKEN,CONTENT-TYPE
        allow-credentials: true
        max-age: 10
      developer-endpoints:
        - paths: /actuator/**,/**/actuator/**
          role: DEVELOPER
      public-endpoints:
        - paths: /**
---
spring:
  config:
    activate:
      on-profile: h2-db
  flyway:
    enabled: true
    locations: classpath:/database/db/migration
  datasource:
    type: org.apache.tomcat.jdbc.pool.DataSource
    driver-class-name: org.h2.Driver
    #url: jdbc:h2:file:./data/journey-import;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    url: jdbc:h2:mem:JOURNEY_IMPORT;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
