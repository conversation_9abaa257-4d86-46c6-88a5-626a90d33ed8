<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE hibernate-reverse-engineering
        SYSTEM "https://hibernate.org/dtd/hibernate-reverse-engineering-3.0.dtd" >

<hibernate-reverse-engineering>

    <schema-selection match-schema="JOURNEY"/>

    <type-mapping>
        <sql-type jdbc-type="NUMERIC" precision="1" scale="0" not-null="true" hibernate-type="java.lang.Integer"/>
        <sql-type jdbc-type="NUMERIC" precision="1" scale="0" hibernate-type="java.lang.Integer"/>
        <sql-type jdbc-type="NUMERIC" precision="2" scale="0" hibernate-type="java.lang.Integer"/>
        <sql-type jdbc-type="NUMERIC" precision="3" scale="0" hibernate-type="java.lang.Integer"/>
        <sql-type jdbc-type="NUMERIC" precision="4" scale="0" not-null="true" hibernate-type="java.lang.Integer"/>
        <sql-type jdbc-type="NUMERIC" precision="4" scale="0" hibernate-type="java.lang.Integer"/>
        <sql-type jdbc-type="NUMERIC" precision="12" scale="2" not-null="true" hibernate-type="java.math.BigDecimal"/>
        <sql-type jdbc-type="NUMERIC" precision="12" scale="2" hibernate-type="java.math.BigDecimal"/>
        <sql-type jdbc-type="NUMERIC" scale="2" not-null="true" hibernate-type="java.math.BigDecimal"/>
        <sql-type jdbc-type="NUMERIC" scale="3" not-null="true" hibernate-type="java.math.BigDecimal"/>
        <sql-type jdbc-type="NUMERIC" scale="4" not-null="true" hibernate-type="java.math.BigDecimal"/>
        <sql-type jdbc-type="NUMERIC" scale="5" not-null="true" hibernate-type="java.math.BigDecimal"/>
        <sql-type jdbc-type="NUMERIC" precision="8" scale="0" hibernate-type="java.lang.Long"/>
        <sql-type jdbc-type="NUMERIC" precision="8" hibernate-type="java.lang.Long"/>
        <sql-type jdbc-type="NUMERIC" precision="10" scale="0" not-null="true" hibernate-type="java.lang.Long"/>
        <sql-type jdbc-type="NUMERIC" precision="10" hibernate-type="java.lang.Long"/>
        <sql-type jdbc-type="NUMERIC" not-null="true" hibernate-type="java.lang.Long"/>
        <sql-type jdbc-type="INTEGER" not-null="true" hibernate-type="java.lang.Integer"/>
        <sql-type jdbc-type="DATE" hibernate-type="java.time.LocalDate"/>
        <sql-type jdbc-type="TIMESTAMP" hibernate-type="java.time.LocalDateTime"/>
        <sql-type jdbc-type="BLOB" hibernate-type="byte[]"/>
        <sql-type jdbc-type="CLOB" hibernate-type="byte[]"/>

    </type-mapping>

    <!-- JOURNEY_CUSTOMER_DEFINITION Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_CUSTOMER_DEFINITION">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="optimizer">none</param>
                <param name="sequence_name">JOURNEY.JOURNEY_CUSTOMER_DEFINITION_SEQ</param>
            </generator>
            <key-column name="CUSTOMER_DEFINITION_ID"/>
        </primary-key>
    </table>

    <!-- JOURNEY_CATEGORY_TYPE Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_CATEGORY_TYPE">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="optimizer">none</param>
                <param name="sequence_name">JOURNEY.JOURNEY_CATEGORY_TYPE_SEQ</param>
            </generator>
            <key-column name="JOURNEY_CATEGORY_TYPE_ID"/>
        </primary-key>
    </table>

    <!-- JOURNEY_CATEGORY Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_CATEGORY">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="optimizer">none</param>
                <param name="sequence_name">JOURNEY.JOURNEY_CATEGORY_SEQ</param>
            </generator>
            <key-column name="JOURNEY_CATEGORY_ID"/>
        </primary-key>
    </table>

    <!-- JOURNEY_MILESTONE Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_MILESTONE">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="optimizer">none</param>
                <param name="sequence_name">JOURNEY.JOURNEY_MILESTONE_SEQ</param>
            </generator>
            <key-column name="JOURNEY_MILESTONE_ID"/>
        </primary-key>
    </table>

    <!-- JOURNEY_RULES Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_RULES">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="optimizer">none</param>
                <param name="sequence_name">JOURNEY.JOURNEY_RULES_SEQ</param>
            </generator>
            <key-column name="JOURNEY_PROGRAM_VERSION_RULES_ID"/>
        </primary-key>
    </table>

    <!-- JOURNEY_PROGRAM Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_PROGRAM">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="optimizer">none</param>
                <param name="sequence_name">JOURNEY.JOURNEY_PROGRAM_SEQ</param>
            </generator>
            <key-column name="JOURNEY_PROGRAM_ID"/>
        </primary-key>
    </table>

    <!-- JOURNEY_PROGRAM_RECOMMENDATION Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_PROGRAM_RECOMMENDATION">
        <primary-key>
            <key-column name="JOURNEY_PROGRAM_ID"/>
            <key-column name="CUSTOMER_ID"/>
        </primary-key>
    </table>

    <!-- JOURNEY_CATEGORIZATION Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_CATEGORIZATION">
        <primary-key>
            <key-column name="JOURNEY_PROGRAM_ID"/>
            <key-column name="JOURNEY_CATEGORY_ID"/>
            <key-column name="EFF_FROM"/>
        </primary-key>
    </table>

    <!-- JOURNEY_PROGRAM_VERSION Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_PROGRAM_VERSION">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="optimizer">none</param>
                <param name="sequence_name">JOURNEY.JOURNEY_PROGRAM_VERSION_SEQ</param>
            </generator>
            <key-column name="JOURNEY_PROGRAM_VERSION_ID"/>
        </primary-key>
    </table>

    <!-- JOURNEY_PROGRAM_MILESTONE Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_PROGRAM_MILESTONE">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="optimizer">none</param>
                <param name="sequence_name">JOURNEY.JOURNEY_PROGRAM_MILESTONE_SEQ</param>
            </generator>
            <key-column name="JOURNEY_PROGRAM_MILESTONE_ID"/>
        </primary-key>
    </table>

    <!-- JOURNEY_PROGRAM_MILESTONE_REWARD_CUSTOMIZATION Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_PROGRAM_MILESTONE_REWARD_CUSTOMIZATION">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="optimizer">none</param>
                <param name="sequence_name">JOURNEY.JOURNEY_PROGRAM_MILESTONE_REWARD_CUSTOMIZATION_SEQ</param>
            </generator>
            <key-column name="JOURNEY_REWARD_CUSTOMIZATION_ID"/>
        </primary-key>
    </table>

    <!-- JOURNEY_PROGRAM_REWARD_CUSTOMIZATION Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_PROGRAM_REWARD_CUSTOMIZATION">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="optimizer">none</param>
                <param name="sequence_name">JOURNEY.JOURNEY_PROGRAM_REWARD_CUSTOMIZATION_SEQ</param>
            </generator>
            <key-column name="JOURNEY_REWARD_CUSTOMIZATION_ID"/>
        </primary-key>
    </table>

    <!-- JOURNEY_ENROLLMENT Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_ENROLLMENT">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="optimizer">none</param>
                <param name="sequence_name">JOURNEY.JOURNEY_ENROLLMENT_SEQ</param>
            </generator>
            <key-column name="JOURNEY_ENROLLMENT_ID"/>
        </primary-key>
    </table>

    <!-- JOURNEY_ENROLLMENT_MILESTONE_AWARD Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_ENROLLMENT_MILESTONE_AWARD">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="optimizer">none</param>
                <param name="sequence_name">JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_AWARD_SEQ</param>
            </generator>
            <key-column name="JOURNEY_ENROLLMENT_MILESTONE_AWARD_ID"/>
        </primary-key>
    </table>

    <!-- JOURNEY_ENROLLMENT_PROGRAM_AWARD Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_ENROLLMENT_PROGRAM_AWARD">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="optimizer">none</param>
                <param name="sequence_name">JOURNEY.JOURNEY_ENROLLMENT_PROGRAM_AWARD_SEQ</param>
            </generator>
            <key-column name="JOURNEY_ENROLLMENT_PROGRAM_AWARD_ID"/>
        </primary-key>
    </table>

    <!-- JOURNEY_ENROLLMENT_MILESTONE Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_ENROLLMENT_MILESTONE">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="optimizer">none</param>
                <param name="sequence_name">JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_SEQ</param>
            </generator>
            <key-column name="JOURNEY_ENROLLMENT_MILESTONE_ID"/>
        </primary-key>
    </table>

    <!-- JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="optimizer">none</param>
                <param name="sequence_name">JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_SEQ</param>
            </generator>
            <key-column name="JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_ID"/>
        </primary-key>
    </table>

    <!-- JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_TXN Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_TXN">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="optimizer">none</param>
                <param name="sequence_name">JOURNEY.JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_TXN_SEQ</param>
            </generator>
            <key-column name="JOURNEY_ENROLLMENT_MILESTONE_ACTIVITY_TXN_ID"/>
        </primary-key>
    </table>

    <!-- JOURNEY_ENROLLMENT_ATTRIBUTE Table -->
    <table schema="JOURNEY" catalog="ENTITY" name="JOURNEY_ENROLLMENT_ATTRIBUTE">
        <primary-key>
            <generator class="org.hibernate.id.enhanced.SequenceStyleGenerator">
                <param name="optimizer">none</param>
                <param name="sequence_name">JOURNEY.JOURNEY_ENROLLMENT_ATTRIBUTE_SEQ</param>
            </generator>
            <key-column name="ID"/>
        </primary-key>
    </table>

</hibernate-reverse-engineering>
