package com.vitality.journey.importer.service.imprt.normalize;

import com.vitality.journey.importer.database.databaseMapping.JourneyMilestoneTemplate;
import com.vitality.journey.importer.database.databaseMapping.JourneyProgramMemberData;
import com.vitality.journey.importer.database.databaseMapping.JourneyProgramTemplate;
import com.vitality.journey.importer.database.databaseMapping.JourneyTemplate;
import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.exception.BusinessRuleException;
import com.vitality.journey.importer.model.JourneyType;
import com.vitality.journey.importer.model.ProgramMemberData;
import com.vitality.journey.importer.service.imprt.EntityCrossmapService;
import com.vitality.journey.importer.service.imprt.staging.StagingRecordReader;
import com.vitality.journey.importer.service.journey.JourneyMemberService;
import com.vitality.journey.importer.service.journey.JourneyTemplateService;
import com.vitality.journey.importer.service.member.MemberService;
import com.vitality.journey.importer.util.DateUtils;
import com.vitality.journey.importer.util.JdbcLobUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;

@Slf4j
@Service
@RequiredArgsConstructor
public class NormalizationDataService {
    public static final int DPP_DURATION_WEEKS = 52;
    private final JourneyTemplateService jtService;
    private final JourneyMemberService jpmdService;
    private final MemberService memberService;
    private final EntityCrossmapService entityCrossmapService;

    @Transactional
    public void normalizeRecord(long jobId, long employerId, StagingRecord stagingRecord) {
        if (!MediaType.APPLICATION_JSON.getSubtype().equals(stagingRecord.getType())) {
            log.warn("Skipping normalization of non-JSON record: {}", stagingRecord);
            return;
        }

        if (log.isDebugEnabled()) {
            log.debug("Normalize staging record: id={}, json={}",
                    stagingRecord.getId(), JdbcLobUtils.readClobAsString(stagingRecord.getPayload()));
        }

        StagingRecordReader recordReader = StagingRecordReader.of(stagingRecord, employerId);

        validate(recordReader);

        Long stagingRecordId = stagingRecord.getId();

        ProgramMemberData programMemberDataDto = recordReader.readMemberProgramData();

        JourneyProgramTemplate programTemplate = getOrCreateProgramTemplate(programMemberDataDto);

        // Member mapping
        Member member = memberService.saveMemberAndReturnEntity(programMemberDataDto.member());

        // Program Member Data and Milestone Member Data mappings
        JourneyProgramMemberData programMemberData = jpmdService.saveProgramMemberData(
                programTemplate.getId(),
                programMemberDataDto
        );

        saveCrossmapRecords(jobId, stagingRecordId, programTemplate, member, programMemberData);
    }

    private JourneyProgramTemplate getOrCreateProgramTemplate(ProgramMemberData memberProgramData) {
        JourneyTemplate journeyTemplate = jtService.findTemplate(JourneyType.DPP);

        LocalDate programStartDate = memberProgramData.programStartDate();
        LocalDate programEndDate = DateUtils.weekIterationWindow(memberProgramData.programStartDate(), 1)[1]
                .plusWeeks(DPP_DURATION_WEEKS);

        return jtService.findOrCreate(
                journeyTemplate,
                memberProgramData.programName(),
                programStartDate,
                programEndDate,
                memberProgramData.getNumberOfIterations()
        );
    }

    private void saveCrossmapRecords(Long importJobId, Long stagingRecordId,
                                     JourneyProgramTemplate programTemplate, Member member,
                                     JourneyProgramMemberData programMemberData) {
        entityCrossmapService.saveCrossmapEntry(importJobId, stagingRecordId, programTemplate.getJourneyTemplate());
        entityCrossmapService.saveCrossmapEntry(importJobId, stagingRecordId, programTemplate);
        for (JourneyMilestoneTemplate milestoneTemplate : programTemplate.getJourneyMilestoneTemplates()) {
            entityCrossmapService.saveCrossmapEntry(importJobId, stagingRecordId, milestoneTemplate);
        }
        entityCrossmapService.saveCrossmapEntry(importJobId, stagingRecordId, member);
        entityCrossmapService.saveCrossmapEntry(importJobId, stagingRecordId, programMemberData);
        programMemberData.getJourneyMilestoneMemberDatas()
                .forEach(milestoneMemberData ->
                        entityCrossmapService.saveCrossmapEntry(importJobId, stagingRecordId, milestoneMemberData));
    }

    private void validate(StagingRecordReader recordParser) {
        ProgramMemberData memberProgramData = recordParser.readMemberProgramData();
        if (!DateUtils.isMonday(memberProgramData.programStartDate())) {
            throw new BusinessRuleException("Program start date must be a Monday");
        }
    }
}
