package com.vitality.journey.importer.service.imprt;

import com.vitality.journey.importer.model.imprt.ExecutionResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameter;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.Map;

import static com.vitality.journey.importer.batch.JobParametersFactory.*;

@Slf4j
@Service
public class ImportOrchestrator {
    private final JobLauncher jobLauncher;
    private final Job dppImportJob;
    private final Job dppEnrollJob;

    public ImportOrchestrator(JobLauncher jobLauncher,
                              @Qualifier("dppImportJob") Job dppImportJob,
                              @Qualifier("dppEnrollJob") Job dppEnrollJob) {
        this.jobLauncher = jobLauncher;
        this.dppImportJob = dppImportJob;
        this.dppEnrollJob = dppEnrollJob;
    }

    public ExecutionResult importDppCsv(String originalFileName, InputStream inputStream, long employerId) {
        log.info("Importing DPP CSV using Spring Batch for employer: {}", employerId);

        try {
            Path tempFile = Files.createTempFile("dpp-import-", ".csv");
            Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING);

            try {
                JobParameters jobParameters = createImportJobParameters(originalFileName, tempFile, employerId);

                JobExecution jobExecution = jobLauncher.run(dppImportJob, jobParameters);

                logJobExecutionResult(jobExecution);

                return new ExecutionResult(jobExecution);
            } finally {
                Files.deleteIfExists(tempFile);
            }
        } catch (Exception e) {
            log.error("Failed to execute Spring Batch job for employer: {}", employerId, e);
            throw new ImportException(dppImportJob.getName() + " job failed", e);
        }
    }

    public ExecutionResult enroll(Long jobExecutionId, Long entityNo) {
        JobParameters jobParameters = createEnrollJobParameters(jobExecutionId, entityNo);

        try {
            JobExecution jobExecution = jobLauncher.run(dppEnrollJob, jobParameters);

            logJobExecutionResult(jobExecution);

            return new ExecutionResult(jobExecution);
        } catch (Exception e) {
            throw new ImportException(dppEnrollJob.getName() + " job failed", e);
        }
    }

    private static void logJobExecutionResult(JobExecution jobExecution) {
        JobParameters parameters = jobExecution.getJobParameters();

        log.info("{} job completed with status: {} params: {}",
            jobExecution.getJobInstance().getJobName(), jobExecution.getExitStatus().getExitCode(), toCompactParams(parameters));
    }

    private static String toCompactParams(JobParameters jobParameters) {
        if (jobParameters == null || jobParameters.getParameters().isEmpty()) {
            return "";
        }

        return jobParameters.getParameters().entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .map(e -> e.getKey() + "=" + stringValue(e.getValue()))
            .reduce((a, b) -> a + "," + b)
            .orElse("");
    }

    private static String stringValue(JobParameter<?> jobParameter) {
        if (jobParameter == null) return "null";
        Object val = jobParameter.getValue();
        return val.toString();
    }
}
