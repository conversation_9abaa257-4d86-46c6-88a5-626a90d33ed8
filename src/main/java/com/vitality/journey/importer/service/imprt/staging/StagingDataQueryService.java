package com.vitality.journey.importer.service.imprt.staging;

import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.database.databaseMapping.StagingRecordError;
import com.vitality.journey.importer.database.repository.StagingRecordRepositoryExtension;
import com.vitality.journey.importer.mapper.StagingRecordMapper;
import com.vitality.journey.importer.model.dto.StagingRecordDto;
import com.vitality.journey.importer.model.dto.StagingRecordErrorDto;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
@RequiredArgsConstructor
public class StagingDataQueryService {

    private final StagingRecordRepositoryExtension stagingRecordRepository;
    private final StagingRecordMapper stagingRecordMapper;

    @Transactional(readOnly = true)
    public Optional<StagingRecordDto> getStagingRecord(Long stagingRecordId) {
        return stagingRecordRepository.findById(stagingRecordId)
            .map(stagingRecordMapper::toDto);
    }

    @Transactional(readOnly = true)
    public PagedModel<StagingRecordDto> getStagingRecords(Long importJobId, Pageable pageable) {
        Page<StagingRecord> page = stagingRecordRepository.findByJobId(importJobId, pageable);
        return new PagedModel<>(page.map(stagingRecordMapper::toDto));
    }

    @Transactional(readOnly = true)
    public PagedModel<StagingRecordErrorDto> getStagingRecordErrors(Long importJobId, Pageable pageable) {
        Page<StagingRecordError> page = stagingRecordRepository.findErrorsByJobId(importJobId, pageable);
        return new PagedModel<>(page.map(stagingRecordMapper::toDto));
    }
}
