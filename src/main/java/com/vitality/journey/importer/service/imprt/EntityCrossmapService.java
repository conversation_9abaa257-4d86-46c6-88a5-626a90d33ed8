package com.vitality.journey.importer.service.imprt;

import com.vitality.journey.importer.database.databaseMapping.JourneyMilestoneMemberData;
import com.vitality.journey.importer.database.databaseMapping.JourneyMilestoneTemplate;
import com.vitality.journey.importer.database.databaseMapping.JourneyProgramMemberData;
import com.vitality.journey.importer.database.databaseMapping.JourneyProgramTemplate;
import com.vitality.journey.importer.database.databaseMapping.JourneyTemplate;
import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.database.databaseMapping.StagingToEntityCrossmap;
import com.vitality.journey.importer.database.databaseMapping.StagingToEntityCrossmapId;
import com.vitality.journey.importer.database.repository.StagingToEntityCrossmapRepositoryExtension;
import jakarta.persistence.Table;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.OptionalLong;

@Slf4j
@Service
@RequiredArgsConstructor
public class EntityCrossmapService {

    public static final String ENTITY_TYPE_BATCH_JOB_EXECUTION = "BATCH_JOB_EXECUTION";

    private final StagingToEntityCrossmapRepositoryExtension crossmapRepository;
    private final Map<Class<?>, String> entityTypeMap = new HashMap<>();

    public OptionalLong getStagingRecordId(Long importJobId, Member member) {
        return crossmapRepository.findByEntityReference(importJobId, member.getId(), getEntityType(member))
                .map(crossmap -> OptionalLong.of(crossmap.getId().getStagingRecordId()))
                .orElseGet(OptionalLong::empty);
    }

    public void saveCrossmapEntry(Long importJobId, Long stagingRecordId) {
        saveCrossmapEntry(importJobId, stagingRecordId, importJobId, ENTITY_TYPE_BATCH_JOB_EXECUTION);
    }

    public void saveCrossmapEntry(Long importJobId, Long stagingRecordId, JourneyProgramMemberData entity) {
        saveCrossmapEntry(importJobId, stagingRecordId, entity.getId(), getEntityType(entity));
    }

    public void saveCrossmapEntry(Long importJobId, Long stagingRecordId, JourneyMilestoneMemberData entity) {
        saveCrossmapEntry(importJobId, stagingRecordId, entity.getId(), getEntityType(entity));
    }

    public void saveCrossmapEntry(Long importJobId, Long stagingRecordId, JourneyTemplate entity) {
        saveCrossmapEntry(importJobId, stagingRecordId, entity.getId(), getEntityType(entity));
    }

    public void saveCrossmapEntry(Long importJobId, Long stagingRecordId, JourneyProgramTemplate entity) {
        saveCrossmapEntry(importJobId, stagingRecordId, entity.getId(), getEntityType(entity));
    }

    public void saveCrossmapEntry(Long importJobId, Long stagingRecordId, JourneyMilestoneTemplate entity) {
        saveCrossmapEntry(importJobId, stagingRecordId, entity.getId(), getEntityType(entity));
    }

    public void saveCrossmapEntry(Long importJobId, Long stagingRecordId, Member entity) {
        saveCrossmapEntry(importJobId, stagingRecordId, entity.getId(), getEntityType(entity));
    }

    private void saveCrossmapEntry(Long importJobId, Long stagingRecordId, Long entityId, String entityType) {
        StagingToEntityCrossmapId id = new StagingToEntityCrossmapId(importJobId, stagingRecordId, entityId, entityType);
        StagingToEntityCrossmap crossmap = new StagingToEntityCrossmap(id, LocalDateTime.now());
        crossmapRepository.save(crossmap);
        log.debug("Saved crossmap entry: importJobId={}, stagingRecordId={}, entityId={}, entityType={}",
                importJobId, stagingRecordId, entityId, entityType);
    }

    private String getEntityType(Object entity) {
        return entityTypeMap.computeIfAbsent(
                entity.getClass(),
                k -> Objects.requireNonNull(entity.getClass().getAnnotation(Table.class).name())
        );
    }
}
