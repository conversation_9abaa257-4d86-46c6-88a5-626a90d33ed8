package com.vitality.journey.importer.service.member;

import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.database.repository.MemberRepositoryExtension;
import com.vitality.journey.importer.mapper.MemberMapper;
import com.vitality.journey.importer.model.MemberRecord;
import com.vitality.journey.importer.model.dto.MemberDto;
import jakarta.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class MemberService {

    private final MemberRepositoryExtension memberRepository;
    private final MemberMapper memberMapper;

    @Transactional(
            noRollbackFor = {DataIntegrityViolationException.class, ConstraintViolationException.class}
    )
    public Member saveMemberAndReturnEntity(MemberRecord dto) {
        return memberRepository.findByUniqueIdAndEmployerId(dto.getUniqueId(), dto.getEmployerId())
                .map(existing -> {
                    memberMapper.mergeToEntity(dto, existing);
                    return memberRepository.saveAndFlush(existing);
                })
                .orElseGet(() -> {
                    Member member;
                    try {
                        member = memberRepository.saveAndFlush(memberMapper.toEntity(dto));
                    } catch (DataIntegrityViolationException ex) {
                        // Concurrent insert happened, load and return
                        member = memberRepository.findByUniqueIdAndEmployerId(dto.getUniqueId(), dto.getEmployerId())
                                .orElseThrow(() -> ex);
                    }
                    return member;
                });
    }

    @Transactional(readOnly = true)
    public Page<MemberDto> getAll(Long employerId, Pageable pageable) {
        Page<Member> page;
        if (employerId != null) {
            page = memberRepository.findByEmployerId(employerId, pageable);
        } else {
            page = memberRepository.findAll(pageable);
        }
        return page.map(memberMapper::toDto);
    }

    @Transactional(readOnly = true)
    public Optional<MemberDto> getByEntityNo(Long id) {
        return memberRepository.findByEntityNo(id).map(memberMapper::toDto);
    }
}
