package com.vitality.journey.importer.controller;

import com.vitality.journey.importer.model.batch.BatchJobDto;
import com.vitality.journey.importer.model.imprt.ExecutionResult;
import com.vitality.journey.importer.model.validation.ValidationErrorResponse;
import com.vitality.journey.importer.service.batch.BatchJobService;
import com.vitality.journey.importer.service.imprt.DataCleanUpService;
import com.vitality.journey.importer.service.imprt.ImportOrchestrator;
import com.vitality.journey.importer.service.validation.FileValidationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;

@Slf4j
@RestController
@RequestMapping("/api/import")
@RequiredArgsConstructor
@Tag(name = "Import", description = "Journey data import operations")
public class ImportController {

    private final ImportOrchestrator importOrchestrator;
    private final DataCleanUpService dataCleanUpService;
    private final FileValidationService fileValidationService;
    private final BatchJobService batchJobService;

    @Operation(
        summary = "Import DPP CSV file",
        description = "Uploads and imports a Diabetes Prevention Program CSV file for the specified employer. The file must be in CSV format. Returns the import job details if successful. As result it will create a personal journey template data for each member from the file."
    )
    @ApiResponse(
        responseCode = "200",
        description = "Import started successfully"
    )
    @ApiResponse(
        responseCode = "400",
        description = "Invalid file format or missing parameters"
    )
    @ApiResponse(
        responseCode = "500",
        description = "Failed to handle uploaded file"
    )
    @PostMapping(value = "/upload/dpp-csv-full", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> importDppCsv(
        @Parameter(description = "Employer identifier", example = "12345", required = true)
        @RequestParam("employerId") long employerId,
        @Parameter(description = "CSV file containing DPP journey data", required = true)
        @RequestParam("file") MultipartFile file) {

        var validationResult = fileValidationService.validateFile(file);
        if (!validationResult.valid()) {
            var errorResponse = new ValidationErrorResponse(
                LocalDateTime.now(),
                400,
                "Validation Failed",
                validationResult.errors(),
                "/api/import/upload/dpp-csv"
            );
            return ResponseEntity.badRequest().body(errorResponse);
        }

        try (var inputStream = file.getInputStream()) {
            ExecutionResult executionResult = importOrchestrator.importDppCsv(
                file.getOriginalFilename(), inputStream, employerId
            );

            return batchJobService.getJobById(executionResult.executionId())
                .map(ResponseEntity::ok)
                .orElseThrow();
        } catch (IOException e) {
            log.error("Failed to handle uploaded file", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @Operation(
        summary = "Enroll imported members",
        description = "Enrolls all imported members in the journey. If entityNo is specified, only that member will be enrolled."
    )
    @ApiResponse(
        responseCode = "200",
        description = "Enrollment completed successfully"
    )
    @GetMapping("/enroll")
    public ResponseEntity<BatchJobDto> enrollImportedMembers(
        @RequestParam(value = "importId", required = false) Long importId,
        @RequestParam(value = "entityNo", required = false) Long entityNo
    ) {
        ExecutionResult executionResult = importOrchestrator.enroll(importId, entityNo);

        return batchJobService.getJobById(executionResult.executionId())
            .map(ResponseEntity::ok)
            .orElseThrow();
    }

    @Operation(
        summary = "Delete all staging and normalized data",
        description = "WARNING: This operation will irreversibly delete all staging and normalized data. Use with caution."
    )
    @ApiResponse(
        responseCode = "200",
        description = "All data deleted successfully"
    )
    @DeleteMapping("/clear")
    public ResponseEntity<Void> deleteAllData() {
        dataCleanUpService.deleteAllData();
        return ResponseEntity.ok().build();
    }
}
