package com.vitality.journey.importer.batch.enroll;

import com.vitality.journey.importer.batch.StagingMemberSkipListener;
import com.vitality.journey.importer.database.repository.StagingRecordRepositoryExtension;
import com.vitality.journey.importer.database.repository.StagingToEntityCrossmapRepositoryExtension;
import com.vitality.journey.importer.service.imprt.staging.StagingRecordUpdater;
import org.springframework.stereotype.Component;

@Component
public class EnrollmentSkipListener extends StagingMemberSkipListener {
    public EnrollmentSkipListener(StagingToEntityCrossmapRepositoryExtension crossmapRepository,
                                  StagingRecordRepositoryExtension stagingRecordRepository,
                                  StagingRecordUpdater stagingRecordUpdater) {
        super(crossmapRepository, stagingRecordRepository, stagingRecordUpdater);
    }
}
