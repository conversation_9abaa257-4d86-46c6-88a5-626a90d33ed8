package com.vitality.journey.importer.batch.stage;

import com.vitality.journey.importer.batch.BatchJobProperties;
import com.vitality.journey.importer.batch.ImportJobStep;
import com.vitality.journey.importer.batch.StepStatusPostProcessor;
import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.service.csv.ParsedCsvRow;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.SimpleStepBuilder;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;

@Component
@RequiredArgsConstructor
public class StagingStepFactory {

    private final CsvFileItemReader csvFileItemReader;
    private final StagingItemProcessor stagingItemProcessor;
    private final StagingItemWriter stagingItemWriter;
    private final StagingSkipListener stagingSkipListener;
    private final StepStatusPostProcessor stepStatusPostProcessor;
    private final StagingDataCleanupTasklet stagingDataCleanupTasklet;
    private final TaskExecutor batchTaskExecutor;
    private final BatchJobProperties batchJobProperties;

    public Step createStagingDataCleanupStep(JobRepository jobRepository, PlatformTransactionManager transactionManager) {
        return new StepBuilder(ImportJobStep.STAGE_DATA_CLEANUP.getStepName(), jobRepository)
            .tasklet(stagingDataCleanupTasklet, transactionManager)
            .build();
    }

    public Step createStagingStep(JobRepository jobRepository, PlatformTransactionManager transactionManager) {
        int chunkSize = batchJobProperties.getDpp().getStaging().getChunkSize();
        boolean isConcurrent = batchJobProperties.getDpp().getStaging().isConcurrent();

        SimpleStepBuilder<ParsedCsvRow, StagingRecord> builder = new StepBuilder(ImportJobStep.STAGE.getStepName(), jobRepository)
            .<ParsedCsvRow, StagingRecord>chunk(chunkSize, transactionManager)
            .reader(csvFileItemReader)
            .processor(stagingItemProcessor)
            .writer(stagingItemWriter);

        if (batchJobProperties.getThreadPoolSize() > 1 && isConcurrent) {
            builder.taskExecutor(batchTaskExecutor);
        }

        return builder
            .faultTolerant()
            .skip(Exception.class)
            .skipLimit(Integer.MAX_VALUE)
            .listener(stagingSkipListener)
            .listener(stepStatusPostProcessor)
            .build();
    }
}
