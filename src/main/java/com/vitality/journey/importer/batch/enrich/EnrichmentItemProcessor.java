package com.vitality.journey.importer.batch.enrich;

import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.mapper.MemberMapper;
import com.vitality.journey.importer.model.MemberRecord;
import com.vitality.journey.importer.service.member.EntityNoResolver;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.OptionalLong;

@Slf4j
@Component
@RequiredArgsConstructor
public class EnrichmentItemProcessor implements ItemProcessor<Member, Member> {
    private final EntityNoResolver entityNoResolver;
    private final MemberMapper memberMapper;

    @Override
    public Member process(@NonNull Member member) {
        MemberRecord mapperRecord = memberMapper.toRecord(member);
        OptionalLong entityNo = entityNoResolver.resolveEntityNo(mapperRecord);

        if (entityNo.isPresent()) {
            member.setEntityNo(entityNo.getAsLong());
            log.debug("Enriched member {} with entityNo {}", member.getUniqueId(), entityNo.getAsLong());
        } else {
            log.warn("Could not resolve entityNo for member {} with employerId {}", member.getUniqueId(), member.getEmployerId());
            throw new EnrichStepException("Failed to resolve entityNo for member: " + member.getUniqueId());
        }

        return member;
    }
}
