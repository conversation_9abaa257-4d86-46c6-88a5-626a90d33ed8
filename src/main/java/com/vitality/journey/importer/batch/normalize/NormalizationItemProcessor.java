package com.vitality.journey.importer.batch.normalize;

import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.model.imprt.RecordStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class NormalizationItemProcessor implements ItemProcessor<StagingRecord, StagingRecord> {

    @Override
    public StagingRecord process(StagingRecord stagingRecord) {
        if (!RecordStatus.LOADED.name().equals(stagingRecord.getStatus())) {
            return null; // Skip non-loaded records
        }
        return stagingRecord; // Pass-through for writer to handle normalization
    }
}