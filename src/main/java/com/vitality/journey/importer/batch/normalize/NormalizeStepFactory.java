package com.vitality.journey.importer.batch.normalize;

import com.vitality.journey.importer.batch.BatchJobProperties;
import com.vitality.journey.importer.batch.ImportJobStep;
import com.vitality.journey.importer.batch.StepStatusPostProcessor;
import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.SimpleStepBuilder;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;

@Component
@RequiredArgsConstructor
public class NormalizeStepFactory {
    private final NormalizationItemReader normalizationItemReader;
    private final NormalizationItemProcessor normalizationItemProcessor;
    private final NormalizationItemWriter normalizationItemWriter;
    private final NormalizationSkipListener normalizationSkipListener;
    private final StepStatusPostProcessor stepStatusPostProcessor;
    private final TaskExecutor batchTaskExecutor;
    private final BatchJobProperties batchJobProperties;

    public Step createStep(JobRepository jobRepository, PlatformTransactionManager transactionManager) {
        int chunkSize = batchJobProperties.getDpp().getNormalize().getChunkSize();
        boolean isConcurrent = batchJobProperties.getDpp().getNormalize().isConcurrent();

        SimpleStepBuilder<StagingRecord, StagingRecord> builder = new StepBuilder(ImportJobStep.NORMALIZE.getStepName(), jobRepository)
            .<StagingRecord, StagingRecord>chunk(chunkSize, transactionManager)
            .reader(normalizationItemReader)
            .processor(normalizationItemProcessor)
            .writer(normalizationItemWriter);

        if (batchJobProperties.getThreadPoolSize() > 1 && isConcurrent) {
            builder.taskExecutor(batchTaskExecutor);
        }

        return builder
            .faultTolerant()
            .skip(Exception.class)
            .skipLimit(Integer.MAX_VALUE)
            .listener(normalizationSkipListener)
            .listener(stepStatusPostProcessor)
            .build();
    }
}
