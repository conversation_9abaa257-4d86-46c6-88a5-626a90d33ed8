package com.vitality.journey.importer.batch.enrich;

import com.vitality.journey.importer.batch.BatchJobProperties;
import com.vitality.journey.importer.batch.ImportJobStep;
import com.vitality.journey.importer.batch.StepStatusPostProcessor;
import com.vitality.journey.importer.database.databaseMapping.Member;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.SimpleStepBuilder;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;

@Component
@RequiredArgsConstructor
public class EnrichStepFactory {
    private final EnrichmentItemReader enrichmentItemReader;
    private final EnrichmentItemProcessor enrichmentItemProcessor;
    private final EnrichmentItemWriter enrichmentItemWriter;
    private final EnrichmentSkipListener enrichmentSkipListener;
    private final StepStatusPostProcessor stepStatusPostProcessor;
    private final TaskExecutor batchTaskExecutor;
    private final BatchJobProperties batchJobProperties;

    public Step createStep(JobRepository jobRepository, PlatformTransactionManager transactionManager) {
        int chunkSize = batchJobProperties.getDpp().getEnrich().getChunkSize();
        boolean isConcurrent = batchJobProperties.getDpp().getEnrich().isConcurrent();

        SimpleStepBuilder<Member, Member> builder = new StepBuilder(ImportJobStep.ENRICH.getStepName(), jobRepository)
            .<Member, Member>chunk(chunkSize, transactionManager)
            .reader(enrichmentItemReader)
            .processor(enrichmentItemProcessor)
            .writer(enrichmentItemWriter);

        if (batchJobProperties.getThreadPoolSize() > 1 && isConcurrent) {
            builder.taskExecutor(batchTaskExecutor);
        }

        return builder
            .faultTolerant()
            .skip(Exception.class)
            .skipLimit(Integer.MAX_VALUE)
            .listener(enrichmentSkipListener)
            .listener(stepStatusPostProcessor)
            .build();
    }
}
