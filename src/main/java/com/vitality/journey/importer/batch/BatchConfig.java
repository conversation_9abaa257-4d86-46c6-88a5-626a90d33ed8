package com.vitality.journey.importer.batch;

import com.vitality.journey.importer.batch.enrich.EnrichStepFactory;
import com.vitality.journey.importer.batch.enroll.EnrollStepFactory;
import com.vitality.journey.importer.batch.normalize.NormalizeStepFactory;
import com.vitality.journey.importer.batch.stage.StagingStepFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class BatchConfig {

    private final StagingStepFactory stagingStepFactory;
    private final NormalizeStepFactory normalizeStepFactory;
    private final EnrichStepFactory enrichStepFactory;
    private final EnrollStepFactory enrollStepFactory;

    @Bean
    public Job dppImportJob(JobRepository jobRepository,
                            Step stagingDataCleanupStep,
                            Step stagingStep,
                            Step normalizeStep,
                            Step enrichStep,
                            JobCompletionListener jobCompletionListener) {
        return new JobBuilder(ImportJob.DPP_CSV_IMPORT.getJobName(), jobRepository)
            .incrementer(new RunIdIncrementer())
            .start(stagingDataCleanupStep)
            .next(stagingStep)
            .next(normalizeStep)
            .next(enrichStep)
            .listener(jobCompletionListener)
            .build();
    }

    @Bean
    public Job dppEnrollJob(JobRepository jobRepository,
                            Step enrollStep,
                            JobCompletionListener jobCompletionListener) {
        return new JobBuilder(ImportJob.DPP_ENROLL.getJobName(), jobRepository)
            .incrementer(new RunIdIncrementer())
            .start(enrollStep)
            .listener(jobCompletionListener)
            .build();
    }

    @Bean
    public Step stagingDataCleanupStep(JobRepository jobRepository, PlatformTransactionManager transactionManager) {
        return stagingStepFactory.createStagingDataCleanupStep(jobRepository, transactionManager);
    }

    @Bean
    public Step stagingStep(JobRepository jobRepository, PlatformTransactionManager transactionManager) {
        return stagingStepFactory.createStagingStep(jobRepository, transactionManager);
    }

    @Bean
    public Step normalizeStep(JobRepository jobRepository, PlatformTransactionManager transactionManager) {
        return normalizeStepFactory.createStep(jobRepository, transactionManager);
    }

    @Bean
    public Step enrichStep(JobRepository jobRepository, PlatformTransactionManager transactionManager) {
        return enrichStepFactory.createStep(jobRepository, transactionManager);
    }

    @Bean
    public Step enrollStep(JobRepository jobRepository, PlatformTransactionManager transactionManager) {
        return enrollStepFactory.createStep(jobRepository, transactionManager);
    }
}
