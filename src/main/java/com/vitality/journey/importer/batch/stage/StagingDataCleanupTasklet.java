package com.vitality.journey.importer.batch.stage;

import com.vitality.journey.importer.batch.JobParametersFactory;
import com.vitality.journey.importer.model.ProcessingMode;
import com.vitality.journey.importer.service.imprt.DataCleanUpService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class StagingDataCleanupTasklet implements Tasklet {

    private final DataCleanUpService dataCleanUpService;

    @Override
    public RepeatStatus execute(@NonNull StepContribution contribution, @NonNull ChunkContext chunkContext) {
        String mode = contribution.getStepExecution()
            .getJobParameters()
            .getString(JobParametersFactory.PARAM_PROCESSING_MODE);

        if (ProcessingMode.FULL.name().equals(mode)) {
            log.info("Running in {} mode, delete all data", mode);
            dataCleanUpService.deleteAllData();
        }

        return RepeatStatus.FINISHED;
    }
}
