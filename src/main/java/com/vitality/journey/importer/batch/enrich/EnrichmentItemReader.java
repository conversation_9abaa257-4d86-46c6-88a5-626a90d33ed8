package com.vitality.journey.importer.batch.enrich;

import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.database.repository.MemberRepositoryExtension;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.batch.item.data.RepositoryItemReader;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class EnrichmentItemReader extends RepositoryItemReader<Member> {
    private static final int PAGE_SIZE = 100;

    public EnrichmentItemReader(MemberRepositoryExtension memberRepository) {
        setRepository(memberRepository);
        setMethodName("findByJobId");
        setSort(Map.of("id", Sort.Direction.ASC));
        setPageSize(PAGE_SIZE);
    }

    @BeforeStep
    public void beforeStep(StepExecution stepExecution) {
        Long jobExecutionId = stepExecution.getJobExecutionId();
        setArguments(List.of(jobExecutionId));
    }
}