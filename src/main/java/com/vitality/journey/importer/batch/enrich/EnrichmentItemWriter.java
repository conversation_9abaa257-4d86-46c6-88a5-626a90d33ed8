package com.vitality.journey.importer.batch.enrich;

import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.database.repository.MemberRepositoryExtension;
import com.vitality.journey.importer.model.imprt.RecordStatus;
import com.vitality.journey.importer.service.imprt.EntityCrossmapService;
import com.vitality.journey.importer.service.imprt.staging.StagingRecordUpdater;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.scope.context.StepContext;
import org.springframework.batch.core.scope.context.StepSynchronizationManager;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class EnrichmentItemWriter implements ItemWriter<Member> {
    private final MemberRepositoryExtension memberRepository;
    private final StagingRecordUpdater stagingRecordUpdater;
    private final EntityCrossmapService entityCrossmapService;

    @Override
    public void write(Chunk<? extends Member> chunk) {
        for (Member member : chunk.getItems()) {
            memberRepository.save(member);
            log.debug("Enriched member {} with entityNo {}", member.getUniqueId(), member.getEntityNo());

            StepContext context = Objects.requireNonNull(StepSynchronizationManager.getContext());
            Long jobExecutionId = context.getStepExecution().getJobExecutionId();

            entityCrossmapService.getStagingRecordId(jobExecutionId, member).ifPresentOrElse(
                    stagingRecordId -> stagingRecordUpdater.updateStatus(stagingRecordId, RecordStatus.ENRICHED),
                    () -> log.warn("Staging record not found for member {} in job {}", member.getUniqueId(), jobExecutionId)
            );
        }

        if (log.isInfoEnabled()) {
            List<Long> memberIds = chunk.getItems().stream().map(Member::getId).toList();
            log.info("Saved a chunk of {} enriched member records - {}", chunk.size(), memberIds);
        }
    }
}