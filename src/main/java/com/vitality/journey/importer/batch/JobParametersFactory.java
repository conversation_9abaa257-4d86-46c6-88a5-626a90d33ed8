package com.vitality.journey.importer.batch;

import com.vitality.journey.importer.model.ProcessingMode;
import lombok.NoArgsConstructor;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;

import java.nio.file.Path;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class JobParametersFactory {
    public static final String PARAM_ORIGINAL_FILE_NAME = "originalFileName";
    public static final String PARAM_FILE_PATH = "filePath";
    public static final String PARAM_EMPLOYER_ID = "employerId";
    public static final String PARAM_ENTITY_NO = "entityNo";
    public static final String PARAM_PROCESSING_MODE = "processingMode";
    public static final String PARAM_JOB_EXECUTION_ID = "jobExecutionId";
    public static final String PARAM_LAUNCH_TIMESTAMP = "launchTimestamp";

    public static JobParameters createImportJobParameters(String originalFileName, Path filePath, Long employerId) {
        return new JobParametersBuilder()
            .addString(PARAM_ORIGINAL_FILE_NAME, originalFileName)
            .addString(PARAM_FILE_PATH, filePath.toString())
            .addLong(PARAM_EMPLOYER_ID, employerId)
            .addLong(PARAM_LAUNCH_TIMESTAMP, System.currentTimeMillis())
            .addString(PARAM_PROCESSING_MODE, ProcessingMode.FULL.name())
            .toJobParameters();
    }

    public static JobParameters createImportJobParameters(Path filePath) {
        return new JobParametersBuilder()
            .addString(PARAM_FILE_PATH, filePath.toString())
            .addLong(PARAM_LAUNCH_TIMESTAMP, System.currentTimeMillis())
            .addString(PARAM_PROCESSING_MODE, ProcessingMode.FULL.name())
            .toJobParameters();
    }

    public static JobParameters createEnrollJobParameters(Long jobExecutionId, Long entityNo) {
        JobParametersBuilder builder = new JobParametersBuilder()
            .addLong(PARAM_LAUNCH_TIMESTAMP, System.currentTimeMillis())
            .addString(PARAM_PROCESSING_MODE, ProcessingMode.FULL.name());

        if (jobExecutionId != null) {
            builder.addLong(PARAM_JOB_EXECUTION_ID, jobExecutionId);
        }

        if (entityNo != null) {
            builder.addLong(PARAM_ENTITY_NO, entityNo);
        }
        return builder
            .toJobParameters();
    }
}
