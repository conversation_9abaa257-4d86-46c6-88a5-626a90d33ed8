package com.vitality.journey.importer.batch.enrich;

import com.vitality.journey.importer.batch.StagingMemberSkipListener;
import com.vitality.journey.importer.database.repository.StagingRecordRepositoryExtension;
import com.vitality.journey.importer.database.repository.StagingToEntityCrossmapRepositoryExtension;
import com.vitality.journey.importer.service.imprt.staging.StagingRecordUpdater;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
public class EnrichmentSkipListener extends StagingMemberSkipListener {
    public EnrichmentSkipListener(StagingToEntityCrossmapRepositoryExtension crossmapRepository,
                                  StagingRecordRepositoryExtension stagingRecordRepository,
                                  StagingRecordUpdater stagingRecordUpdater) {
        super(crossmapRepository, stagingRecordRepository, stagingRecordUpdater);
    }
}
