package com.vitality.journey.importer.batch;

import lombok.NoArgsConstructor;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.scope.context.StepContext;
import org.springframework.batch.core.scope.context.StepSynchronizationManager;

import java.util.Objects;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class BatchJobExecutionUtils {

    public static Long getStepExecutionId() {
        StepContext context = Objects.requireNonNull(StepSynchronizationManager.getContext());
        StepExecution stepExecution = context.getStepExecution();
        return stepExecution.getId();
    }

    public static Long getJobExecutionId() {
        StepContext context = Objects.requireNonNull(StepSynchronizationManager.getContext());
        StepExecution stepExecution = context.getStepExecution();

        JobParameters params = stepExecution.getJobParameters();

        Long jobExecutionId = params.getLong(JobParametersFactory.PARAM_JOB_EXECUTION_ID);
        if (jobExecutionId == null) {
            jobExecutionId = stepExecution.getJobExecutionId();
        }
        return jobExecutionId;
    }
}
