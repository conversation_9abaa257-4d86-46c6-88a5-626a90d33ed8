package com.vitality.journey.importer.batch.normalize;

import com.vitality.journey.importer.batch.JobParametersFactory;
import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.database.repository.StagingRecordRepositoryExtension;
import com.vitality.journey.importer.model.imprt.RecordStatus;
import com.vitality.journey.importer.service.imprt.normalize.NormalizationDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class NormalizationItemWriter implements ItemWriter<StagingRecord> {

    private final NormalizationDataService normalizationDataService;
    private final StagingRecordRepositoryExtension stagingRecordRepository;

    private Long jobExecutionId;
    private Long employerId;

    @BeforeStep
    public void beforeStep(StepExecution stepExecution) {
        this.jobExecutionId = stepExecution.getJobExecutionId();
        Long employerIdParam = stepExecution.getJobParameters().getLong(JobParametersFactory.PARAM_EMPLOYER_ID);
        this.employerId = Objects.requireNonNull(employerIdParam, "Employer ID not found in job parameters");
    }

    @Override
    public void write(Chunk<? extends StagingRecord> chunk) {
        for (StagingRecord stagingRecord : chunk.getItems()) {
            // Perform normalization using existing service
            normalizationDataService.normalizeRecord(jobExecutionId, employerId, stagingRecord);

            // Update staging record status to NORMALIZED
            stagingRecord.setStatus(RecordStatus.NORMALIZED.name());
            stagingRecordRepository.save(stagingRecord);

            log.debug("Normalized staging record {}", stagingRecord.getId());
        }

        if (log.isInfoEnabled()) {
            List<Long> stagingRecordIds = chunk.getItems().stream().map(StagingRecord::getId).toList();
            log.info("Normalized a chunk of {} staging records for employer {} - {}", chunk.size(), employerId, stagingRecordIds);
        }
    }
}