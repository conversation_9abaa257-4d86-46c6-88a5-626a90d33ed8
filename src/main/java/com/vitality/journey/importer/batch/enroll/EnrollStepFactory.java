package com.vitality.journey.importer.batch.enroll;

import com.vitality.journey.importer.batch.BatchJobProperties;
import com.vitality.journey.importer.batch.ImportJobStep;
import com.vitality.journey.importer.batch.StepStatusPostProcessor;
import com.vitality.journey.importer.database.databaseMapping.Member;
import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.SimpleStepBuilder;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;

@Component
@RequiredArgsConstructor
public class EnrollStepFactory {
    private final EnrollmentItemReader enrollmentItemReader;
    private final EnrollmentItemProcessor enrollmentItemProcessor;
    private final EnrollmentItemWriter enrollmentItemWriter;
    private final EnrollmentSkipListener enrollmentSkipListener;
    private final StepStatusPostProcessor stepStatusPostProcessor;
    private final TaskExecutor batchTaskExecutor;
    private final BatchJobProperties batchJobProperties;

    public Step createStep(JobRepository jobRepository, PlatformTransactionManager transactionManager) {
        int chunkSize = batchJobProperties.getDpp().getEnroll().getChunkSize();
        boolean isConcurrent = batchJobProperties.getDpp().getEnroll().isConcurrent();

        SimpleStepBuilder<Member, Member> builder = new StepBuilder(ImportJobStep.ENROLL.getStepName(), jobRepository)
            .<Member, Member>chunk(chunkSize, transactionManager)
            .reader(enrollmentItemReader)
            .processor(enrollmentItemProcessor)
            .writer(enrollmentItemWriter);

        if (batchJobProperties.getThreadPoolSize() > 1 && isConcurrent) {
            builder.taskExecutor(batchTaskExecutor);
        }

        return builder
            .faultTolerant()
            .skip(Exception.class)
            .skipLimit(Integer.MAX_VALUE)
            .listener(enrollmentSkipListener)
            .listener(stepStatusPostProcessor)
            .build();
    }
}
