package com.vitality.journey.importer.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class StepStatusPostProcessor implements StepExecutionListener {
    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        long readCount = stepExecution.getReadCount();
        long writeCount = stepExecution.getWriteCount();
        long skipCount = stepExecution.getSkipCount();

        if (readCount > 0 && writeCount == 0 && skipCount == readCount) {
            stepExecution.setStatus(BatchStatus.FAILED);
            String description = "All " + readCount + " records failed; step " + stepExecution.getStepName() + " is " + ExitStatus.FAILED;
            return ExitStatus.FAILED.addExitDescription(description);
        }

        ExitStatus currentExitStatus = stepExecution.getExitStatus();
        if (skipCount > 0) {
            String errorMessage = String.format("Failed to process %d records", skipCount);
            return currentExitStatus.addExitDescription(errorMessage);
        } else {
            return currentExitStatus;
        }
    }
}
