package com.vitality.journey.importer.batch.enroll;

import com.vitality.journey.importer.batch.BatchJobExecutionUtils;
import com.vitality.journey.importer.batch.JobParametersFactory;
import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.database.repository.MemberRepositoryExtension;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.batch.item.data.RepositoryItemReader;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class EnrollmentItemReader extends RepositoryItemReader<Member> {
    private static final int PAGE_SIZE = 100;

    public EnrollmentItemReader(MemberRepositoryExtension memberRepository) {
        setRepository(memberRepository);
        setMethodName(MemberRepositoryExtension.FIND_BY_JOB_ID);
        setSort(Map.of("id", Sort.Direction.ASC));
        setPageSize(PAGE_SIZE);
    }

    @BeforeStep
    public void beforeStep(StepExecution stepExecution) {
        Long jobExecutionId = BatchJobExecutionUtils.getJobExecutionId();

        JobParameters params = stepExecution.getJobParameters();
        Long entityNo = params.getLong(JobParametersFactory.PARAM_ENTITY_NO);

        if (entityNo != null) {
            setMethodName(MemberRepositoryExtension.FIND_BY_JOB_ID_AND_ENTITY_NO);
            setArguments(List.of(jobExecutionId, entityNo));
        } else {
            setMethodName(MemberRepositoryExtension.FIND_BY_JOB_ID);
            setArguments(List.of(jobExecutionId));
        }
    }
}
