package com.vitality.journey.importer.batch.enroll;

import com.vitality.journey.importer.batch.BatchJobExecutionUtils;
import com.vitality.journey.importer.batch.JobParametersFactory;
import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.database.repository.MemberRepositoryExtension;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.batch.item.data.RepositoryItemReader;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class EnrollmentItemReader extends RepositoryItemReader<Member> {
    private static final int PAGE_SIZE = 100;

    public EnrollmentItemReader(MemberRepositoryExtension memberRepository) {
        setRepository(memberRepository);
        setMethodName(MemberRepositoryExtension.FIND_BY_JOB_ID);
        setSort(Map.of("id", Sort.Direction.ASC));
        setPageSize(PAGE_SIZE);
    }

    @BeforeStep
    public void beforeStep(StepExecution stepExecution) {
        JobParameters params = stepExecution.getJobParameters();
        Long entityNo = params.getLong(JobParametersFactory.PARAM_ENTITY_NO);

        String methodName;
        List<?> arguments;
        if (entityNo != null) {
            methodName = MemberRepositoryExtension.FIND_BY_ENTITY_NO;
            arguments = List.of(entityNo);
        } else {
            methodName = MemberRepositoryExtension.FIND_BY_JOB_ID;
            arguments = List.of(BatchJobExecutionUtils.getJobExecutionId());
        }
        setMethodName(methodName);
        setArguments(arguments);

        if (log.isInfoEnabled()) {
            String argsLine = arguments.stream().map(Object::toString).collect(Collectors.joining(","));
            log.info("{} initialized to use {}#{}({})",
                getClass().getSimpleName(),
                MemberRepositoryExtension.class.getSimpleName(),
                methodName, argsLine);
        }
    }
}
