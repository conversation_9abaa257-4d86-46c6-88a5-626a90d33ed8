package com.vitality.journey.importer.batch.enroll;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitality.journey.importer.client.PersonalJourneyClientProperties;
import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.model.dto.MemberDto;
import com.vitality.journey.importer.model.dto.MemberJourneyTemplateDto;
import com.vitality.journey.importer.service.journey.JourneyMemberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Map;

@Slf4j
@Component
public class EnrollmentItemProcessor implements ItemProcessor<Member, Member> {

    private static final String JOURNEY_ENROLL_API = "/api/v1/journey/import";

    private final JourneyMemberService journeyMemberService;
    private final ObjectMapper objectMapper;
    private final WebClient webClient;

    public EnrollmentItemProcessor(JourneyMemberService journeyMemberService,
                                   PersonalJourneyClientProperties personalJourneyClientProperties,
                                   ObjectMapper objectMapper,
                                   @Qualifier("b2b-webclient") WebClient webClient) {
        this.journeyMemberService = journeyMemberService;
        this.objectMapper = objectMapper;
        this.webClient = webClient.mutate()
            .baseUrl(personalJourneyClientProperties.getUrl())
            .build();
    }

    @Override
    public Member process(Member member) {
        String journeyType = "DPP";
        journeyMemberService.getJourneyTemplateDto(member.getEntityNo(), journeyType)
            .ifPresentOrElse(this::enrollMemberInJourney,
                () -> log.info("No existing {} journey found for member {}, proceeding with enrollment", journeyType, member.getUniqueId()));

        return member;
    }

    private void enrollMemberInJourney(MemberJourneyTemplateDto journeyTemplate) {
        MemberDto member = journeyTemplate.member();

        try {
            var request = Map.of("journeyTemplate", journeyTemplate);

            String json = objectMapper.writeValueAsString(request);
            log.info("Enrolling member {} in {} journey with request: {}", member.getUniqueId(), journeyTemplate.journeyType(), json);

            webClient.post()
                .uri(JOURNEY_ENROLL_API)
                .bodyValue(request)
                .retrieve()
                .toBodilessEntity()
                .block();

            log.info("Successfully enrolled member {} in {} journey",
                member.getEntityNo(), journeyTemplate.journeyType());
        } catch (Exception e) {
            log.error("Failed to enroll member {} in {} journey: {}",
                member.getEntityNo(), journeyTemplate.journeyType(), e.getMessage(), e);
            throw new EnrollmentStepException("Journey enrollment failed for member: " + member.getUniqueId(), e);
        }
    }
}
