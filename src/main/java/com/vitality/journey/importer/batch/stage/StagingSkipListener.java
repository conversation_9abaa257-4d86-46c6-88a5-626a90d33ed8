package com.vitality.journey.importer.batch.stage;

import com.vitality.journey.importer.batch.BatchJobExecutionUtils;
import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.service.imprt.staging.StagingRecordUpdater;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.SkipListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class StagingSkipListener implements SkipListener<Object, StagingRecord> {

    private final StagingRecordUpdater stagingRecordUpdater;

    @Override
    public void onSkipInWrite(StagingRecord item, @NonNull Throwable t) {
        log.error("Skipped staging record {} due to write error", item.getId(), t);
        stagingRecordUpdater.saveError(item, BatchJobExecutionUtils.getStepExecutionId(), t);
    }
}
