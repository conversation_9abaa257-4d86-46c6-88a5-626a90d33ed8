package com.vitality.journey.importer.batch;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.observability.BatchMetrics;
import org.springframework.stereotype.Component;

import java.time.Duration;

@Slf4j
@Component
public class JobCompletionListener implements JobExecutionListener {

    @Override
    public void beforeJob(JobExecution jobExecution) {
        log.info("=== Job Starting ====");
        log.info("Job: {} - Execution ID: {}", jobExecution.getJobInstance().getJobName(), jobExecution.getJobId());
        log.info("Parameters: {}", jobExecution.getJobParameters().getParameters());
        log.info("=====================");
    }

    @Override
    public void afterJob(JobExecution jobExecution) {
        log.info("=== Job Completion Summary ===");
        log.info("Job: {} - Status: {}", jobExecution.getJobInstance().getJobName(), jobExecution.getStatus());
        log.info("Job Execution ID: {}", jobExecution.getJobId());
        if (jobExecution.getStartTime() != null && jobExecution.getEndTime() != null) {
            Duration duration = Duration.between(jobExecution.getStartTime(), jobExecution.getEndTime());
            log.info("Duration: {}", BatchMetrics.formatDuration(duration));
        }

        for (StepExecution stepExecution : jobExecution.getStepExecutions()) {
            log.info("Step: {} - Read: {}, Written: {}, Skipped: {}, Status: {}",
                    stepExecution.getStepName(),
                    stepExecution.getReadCount(),
                    stepExecution.getWriteCount(),
                    stepExecution.getReadSkipCount() + stepExecution.getWriteSkipCount() + stepExecution.getProcessSkipCount(),
                    stepExecution.getStatus()
            );
        }
        log.info("===============================");
    }
}