package com.vitality.journey.importer.database.repository;

import com.vitality.journey.importer.database.databaseMapping.Member;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

public interface MemberRepositoryExtension extends MemberRepository {

    Optional<Member> findByEntityNo(Long entityNo);

    Optional<Member> findByUniqueIdAndEmployerId(String uniqueId, Long employerId);

    @Query("SELECT m FROM Member m WHERE m.employerId = :employerId ORDER BY m.id")
    Page<Member> findByEmployerId(Long employerId, Pageable pageable);

    String FIND_BY_JOB_ID = "findByJobId";

    @Query("SELECT m FROM Member m JOIN StagingToEntityCrossmap c " +
        "ON m.id = c.id.entityRefId " +
        "WHERE c.id.importJobId = :importJobId AND c.id.entityRefType = 'MEMBER' " +
        "ORDER BY m.id")
    Page<Member> findByJobId(@Param("importJobId") Long importJobId, Pageable pageable);


    String FIND_BY_JOB_ID_AND_ENTITY_NO = "findByJobIdAndEntityNo";

    @Query("SELECT m FROM Member m JOIN StagingToEntityCrossmap c " +
        "ON m.id = c.id.entityRefId " +
        "WHERE c.id.importJobId = :importJobId AND c.id.entityRefType = 'MEMBER' " +
        "AND m.entityNo = :entityNo " +
        "ORDER BY m.id")
    Page<Member> findByJobIdAndEntityNo(Long importJobId, Long entityNo, Pageable pageable);
}
