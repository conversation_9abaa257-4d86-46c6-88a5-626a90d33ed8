package com.vitality.journey.importer.database.repository;

import com.vitality.journey.importer.database.databaseMapping.StagingToEntityCrossmap;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

public interface StagingToEntityCrossmapRepositoryExtension extends StagingToEntityCrossmapRepository {

    @Query("SELECT c FROM StagingToEntityCrossmap c " +
            "WHERE c.id.importJobId = :importJobId " +
            "AND c.id.entityRefId = :entityId " +
            "AND c.id.entityRefType = :entityType")
    Optional<StagingToEntityCrossmap> findByEntityReference(long importJobId, long entityId, String entityType);
}
