package za.co.discovery.health.journey.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import za.co.discovery.health.journey.model.preview.JourneyCategoryPreview;
import za.co.discovery.health.journey.model.preview.LimitedJourneyCategoryPreview;
import za.co.discovery.health.journey.rule.conversion.ConfigFlagJexlRunner;
import za.co.discovery.health.journey.service.PreviewJourneyCategoryService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/api/v1/journey/preview", produces = MediaType.APPLICATION_JSON_VALUE)
public class JourneyCategoryPreviewController {

    private final PreviewJourneyCategoryService journeyCategoryService;

    private final ConfigFlagJexlRunner configFlagJexlRunner;

    @GetMapping("/categories")
    public List<LimitedJourneyCategoryPreview> getJourneyCategories(
            @RequestParam final Long entityId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
                    final LocalDateTime localDateTime) {
        if (configFlagJexlRunner.run(entityId, "journey_enabled")) {
            return journeyCategoryService.getLimitedJourneyCategoriesPreview(
                    entityId, Objects.requireNonNullElse(localDateTime, LocalDateTime.now()));
        }

        return List.of();
    }

    @GetMapping("/categories/{categoryId}")
    public Optional<LimitedJourneyCategoryPreview> getJourneyCategory(
            @RequestParam final Long entityId,
            @PathVariable final Long categoryId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
                    final LocalDateTime localDateTime) {
        if (configFlagJexlRunner.run(entityId, "journey_enabled")) {
            return journeyCategoryService.getLimitedJourneyCategoryPreview(
                    entityId, categoryId, Objects.requireNonNullElse(localDateTime, LocalDateTime.now()));
        }
        return Optional.empty();
    }

    @GetMapping("/categories/{categoryId}/details")
    public Optional<JourneyCategoryPreview> getJourneyCategoryDetails(
            @RequestParam final Long entityId,
            @PathVariable final Long categoryId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
                    final LocalDateTime localDateTime) {
        if (configFlagJexlRunner.run(entityId, "journey_enabled")) {
            return journeyCategoryService.getJourneyCategoryPreview(
                    entityId, categoryId, Objects.requireNonNullElse(localDateTime, LocalDateTime.now()));
        }
        return Optional.empty();
    }
}
