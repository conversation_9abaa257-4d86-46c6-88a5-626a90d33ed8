package za.co.discovery.health.journey.service.journey;

import com.vitality.journey.importer.domain.MemberDto;
import com.vitality.journey.importer.domain.MemberJourneyTemplateDto;
import com.vitality.journey.importer.domain.MemberMilestoneDto;
import com.vitality.journey.importer.domain.MemberProgramDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import za.co.discovery.health.journey.constant.JourneyAttr;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestone;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestoneActivity;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgram;
import za.co.discovery.health.journey.event.EnrollmentCreatedEvent;
import za.co.discovery.health.journey.event.JourneyEventPublisher;
import za.co.discovery.health.journey.event.NextMilestoneNeededEvent;
import za.co.discovery.health.journey.model.request.CompleteMilestoneActivity;
import za.co.discovery.health.journey.service.CdcIdentifierService;
import za.co.discovery.health.journey.service.journey.processor.JourneyCategoryService;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.Objects.requireNonNull;

@Slf4j
@Service
@RequiredArgsConstructor
public class JourneyImporterService {

    private final JourneyCategoryService categoryService;
    private final JourneyEnrollmentService enrollmentService;
    private final JourneyProgramService programService;
    private final JourneyActivityService activityService;
    private final JourneyEventPublisher eventPublisher;
    private final CdcIdentifierService cdcIdService;

    @Transactional
    public void importJourney(final MemberJourneyTemplateDto memberJourneyTemplate) {
        final JourneyEnrollment enrollment = createJourneyFromTemplate(memberJourneyTemplate);

        log.info("Enrollment created event published {}", enrollment);
    }

    private JourneyEnrollment createJourneyFromTemplate(final MemberJourneyTemplateDto journeyTemplate) {
        final MemberDto member = requireNonNull(journeyTemplate.getMember());
        final long entityNo = requireNonNull(member.getEntityNo());

        log.info(
            "Processing journey template '{}' for member: {}",
            journeyTemplate.getJourneyName(),
            entityNo);

        JourneyEnrollment enrollment = createJourneyEnrollment(journeyTemplate);

        log.info("Journey enrollment created for user: {}", entityNo);

        saveJourneyActivities(enrollment, journeyTemplate);

        log.info("Journey activities are saved for member: {}", entityNo);

        return enrollment;
    }

    private JourneyEnrollment createJourneyEnrollment(final MemberJourneyTemplateDto journeyTemplate) {
        final MemberDto member = requireNonNull(journeyTemplate.getMember());
        final long entityNo = requireNonNull(member.getEntityNo());
        final JourneyCategory journey = findJourneyCategory(journeyTemplate);
        final MemberProgramDto memberProgram = requireNonNull(journeyTemplate.getProgram());
        final JourneyProgram firstProgram = findFirstProgram(journey, memberProgram.getProgramStartDate(), entityNo);

        final int deletedJourneys = enrollmentService.deleteUserEnrollments(
            entityNo, journey.getJourneyCategoryId());

        if (deletedJourneys > 0) {
            log.info("Deleted {} existing enrollments for journey: {} and member: {}",
                deletedJourneys, journey.getJourneyCategoryId(), entityNo);
        }

        final Map<String, String> attributes = buildAttributes(journeyTemplate.getAttributes(), member);
        final JourneyEnrollment enrollment = enrollmentService.createEnrollment(
            entityNo, firstProgram, journey, memberProgram.getProgramStartDate(), attributes);

        log.info("Created enrollment {} for member: {}", enrollment.getJourneyEnrollmentId(), entityNo);

        createEnrollmentMilestones(enrollment, memberProgram);
        return enrollment;
    }

    private void createEnrollmentMilestones(JourneyEnrollment enrollment, MemberProgramDto memberProgram) {
        eventPublisher.publish(new EnrollmentCreatedEvent(enrollment, memberProgram.getProgramStartDate()));

        final int maxIteration = getMaxIteration(memberProgram, enrollment.getEntityId());

        // first iteration created automatically on EnrollmentCreatedEvent
        for (int iteration = 2; iteration <= maxIteration; iteration++) {
            eventPublisher.publish(new NextMilestoneNeededEvent(enrollment, iteration, memberProgram.getProgramStartDate()));
        }
    }

    private static int getMaxIteration(MemberProgramDto memberProgram, long entityNo) {
        List<MemberMilestoneDto> milestones = requireNonNull(memberProgram.getMilestones());
        return milestones.stream()
            .filter(Objects::nonNull)
            .map(MemberMilestoneDto::getIteration)
            .filter(Objects::nonNull)
            .mapToInt(Integer::intValue)
            .max()
            .orElseThrow(() -> new IllegalArgumentException("No milestones to import for member: " + entityNo));
    }

    private JourneyProgram findFirstProgram(final JourneyCategory journeyCategory,
                                            final LocalDateTime startDate,
                                            final long entityNo) {
        final JourneyProgram firstProgram =
            programService.getFirstProgram(journeyCategory, startDate);

        if (firstProgram == null) {
            log.error("First program not found for journey: {}, member: {}", journeyCategory.getName(), entityNo);
            throw new IllegalArgumentException("First program not found for journey: " + journeyCategory.getName() + ", member: " + entityNo);
        }

        log.info(
            "First program for journey: '{}' is '{}' ({}), start date: {}",
            firstProgram.getName(),
            firstProgram.getName(),
            firstProgram.getJourneyProgramId(),
            startDate);
        return firstProgram;
    }

    private JourneyCategory findJourneyCategory(MemberJourneyTemplateDto memberJourneyTemplate) {
        MemberDto member = requireNonNull(memberJourneyTemplate.getMember());
        final long entityNo = requireNonNull(member.getEntityNo());

        final JourneyCategory journeyCategory = categoryService.findByNameAndType(
            memberJourneyTemplate.getJourneyName(), memberJourneyTemplate.getJourneyType());

        if (journeyCategory == null) {
            log.error("Journey category not found for journey: {}, member: {}", memberJourneyTemplate.getJourneyName(), entityNo);
            throw new IllegalArgumentException("Journey category not found for journey: " + memberJourneyTemplate.getJourneyName() + ", member: " + entityNo);
        }
        return journeyCategory;
    }

    private Map<String, String> buildAttributes(
        final Map<String, String> providedAttributes, final MemberDto member) {
        final Map<String, String> attributes = new HashMap<>();
        if (providedAttributes != null) {
            attributes.putAll(providedAttributes);

            String cdcId = member.getCdcId();
            if (!StringUtils.hasText(cdcId)) {
                cdcId = cdcIdService.getCdcIdentifier(member.getEntityNo());
            }

            attributes.put(JourneyAttr.CDC_IDENTIFIER.getName(), cdcId);
        }
        return attributes;
    }

    private void saveJourneyActivities(JourneyEnrollment enrollment, final MemberJourneyTemplateDto journeyTemplate) {
        MemberProgramDto programDto = requireNonNull(journeyTemplate.getProgram());
        List<MemberMilestoneDto> milestoneDtoList = requireNonNull(programDto.getMilestones());

        Map<Integer, JourneyEnrollmentMilestone> milestoneEntityMap = enrollment.getJourneyEnrollmentMilestones().stream()
            .collect(Collectors.toMap(journeyEnrollmentMilestone
                -> (int) journeyEnrollmentMilestone.getMilestoneIteration(), Function.identity()));

//        saveInitialData(milestoneEntityMap.get(1), programDto);

        for (MemberMilestoneDto milestoneDto : milestoneDtoList) {
            JourneyEnrollmentMilestone milestone = milestoneEntityMap.get(milestoneDto.getIteration());
            saveMilestoneActivities(milestone, milestoneDto);
        }
    }

    private void saveInitialData(final JourneyEnrollmentMilestone firstMilestone,
                                 final MemberProgramDto programDto) {
        Float initialWeight = programDto.getStartingWeight();
        if (initialWeight != null) {
            getActivityByMnemonic(firstMilestone, "INIT_WEIGHT")
                .ifPresentOrElse(activity -> {
                    Long id = activity.getJourneyEnrollmentMilestoneActivityId();
                    long entityId = firstMilestone.getJourneyEnrollment().getEntityId();

                    CompleteMilestoneActivity completeActivityRequest = new CompleteMilestoneActivity();
                    completeActivityRequest.setMilestoneActivityId(id);
                    completeActivityRequest.setEntityId(entityId);
                    completeActivityRequest.setCompletedAt(programDto.getProgramStartDate());
                    completeActivityRequest.setValue(String.valueOf(initialWeight.intValue()));

                    activityService.processActivityCompletion(completeActivityRequest, false);
                }, () -> log.warn("INIT_WEIGHT activity not found for milestone 1, cannot set initial weight"));
        }
    }

    private void saveMilestoneActivities(final JourneyEnrollmentMilestone milestone,
                                         final MemberMilestoneDto milestoneDto) {
        saveWeight(milestone, milestoneDto);
        savePhysicalActivityMinutes(milestone, milestoneDto);

        final long entityId = milestone.getJourneyEnrollment().getEntityId();
        log.info("Saved milestone activities for user {}", entityId);
    }

    private void saveWeight(JourneyEnrollmentMilestone milestone, MemberMilestoneDto milestoneDto) {
        final long entityId = milestone.getJourneyEnrollment().getEntityId();

        Double weight = milestoneDto.getWeight();
        if (weight != null) {
            getActivityByMnemonic(milestone, "MNWI")
                .ifPresentOrElse(activity ->
                        activityService.processActivityWithValidation(
                            activity,
                            activity.getJourneyEnrollmentMilestoneActivityId(),
                            String.valueOf(weight.intValue()),
                            milestoneDto.getStartDate(),
                            entityId
                        ),
                    () -> log.warn("MNWI activity not found for user {} milestone {}, cannot set weight",
                        entityId, milestone.getMilestoneIteration()));
        }
    }

    private void savePhysicalActivityMinutes(JourneyEnrollmentMilestone milestone, MemberMilestoneDto milestoneDto) {
        final long entityId = milestone.getJourneyEnrollment().getEntityId();

        Integer activityMinutes = milestoneDto.getActivityMinutes();
        if (activityMinutes != null) {
            getActivityByMnemonic(milestone, "SWDA")
                .ifPresentOrElse(activity ->
                        activityService.processActivityWithValidation(
                            activity,
                            activity.getJourneyEnrollmentMilestoneActivityId(),
                            String.valueOf(activityMinutes),
                            milestoneDto.getStartDate(),
                            entityId
                        ),
                    () -> log.warn("SWDA activity not found for user {} milestone {}, cannot set physical activity",
                        entityId, milestone.getMilestoneIteration()));
        }
    }

    private void saveSessionAttendance(JourneyEnrollmentMilestone milestone, MemberMilestoneDto milestoneDto) {
        final long entityId = milestone.getJourneyEnrollment().getEntityId();

        switch (milestoneDto.getAttendanceMode()) {
            case "LIVE":
                break;
            case "RECORDING":
                break;
            default:
                break;
        }
    }

    private Optional<JourneyEnrollmentMilestoneActivity> getActivityByMnemonic(final JourneyEnrollmentMilestone milestone,
                                                                               final String mnemonic) {
        return milestone.getJourneyEnrollmentMilestoneActivities().stream()
            .filter(activity -> mnemonic.equals(activity.getActivityMnemonicId()))
            .findFirst();
    }
}
