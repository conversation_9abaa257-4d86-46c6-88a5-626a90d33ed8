package za.co.discovery.health.journey.messaging.listener;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.facade.ActivityCompletionFacade;
import za.co.discovery.health.journey.messaging.model.ActivityCompletedEvent;
import za.co.discovery.health.journey.messaging.model.PatientAppointmentEvent;

import static za.co.discovery.health.journey.config.KafkaTopics.ACTIVITY_TRANSACTION_EVENT;
import static za.co.discovery.health.journey.config.KafkaTopics.MEMBER_JOINED_CALL_EVENT;

@Profile("kafka")
@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityListener {

    private final ObjectMapper objectMapper;
    private final ActivityCompletionFacade activityCompletionFacade;

    @KafkaListener(
            topics = ACTIVITY_TRANSACTION_EVENT,
            groupId = "${spring.kafka.consumer.group-id}",
            containerFactory = "defaultContainerFactory")
    public void onActivityCompleted(final String message) throws JsonProcessingException {
        log.info("Received ActivityCompletedEvent: {}", message);
        final ActivityCompletedEvent event = objectMapper.readValue(message, ActivityCompletedEvent.class);
        activityCompletionFacade.completeActivity(event);
    }

    @KafkaListener(
            topics = MEMBER_JOINED_CALL_EVENT,
            groupId = "${spring.kafka.consumer.group-id}",
            containerFactory = "defaultContainerFactory")
    public void onPatientAppointmentEvent(final String message) throws JsonProcessingException {
        log.info("Received PatientAppointmentEvent: {}", message);
        final PatientAppointmentEvent event = objectMapper.readValue(message, PatientAppointmentEvent.class);
        activityCompletionFacade.completeAppointment(event);
    }
}
