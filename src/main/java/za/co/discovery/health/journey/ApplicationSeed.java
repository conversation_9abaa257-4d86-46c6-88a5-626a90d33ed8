// package za.co.discovery.health.journey;
//
// import lombok.RequiredArgsConstructor;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.boot.CommandLineRunner;
// import org.springframework.context.annotation.Profile;
// import org.springframework.core.io.ClassPathResource;
// import org.springframework.stereotype.Component;
// import za.co.discovery.health.journey.rule.cache.RuleCacheService;
// import za.co.discovery.health.journey.service.bo.excel.ExcelImportService;
//
// import java.io.File;
//
// @Slf4j
// @Component
// @Profile("local")
// @SuppressWarnings("PMD")
// @RequiredArgsConstructor
// public class ApplicationSeed implements CommandLineRunner {
//    private final ExcelImportService excelImportService;
//    private final RuleCacheService ruleCacheService;
//
//    @Override
//    public void run(final String... args) {
//        try {
//            // Load the spreadsheet from the resources directory
//            final ClassPathResource resource = new ClassPathResource("dummpy_import.xlsx");
//            final File excelFile = resource.getFile();
//
//            // Import the Excel file
//            excelImportService.importExcel(excelFile);
//            ruleCacheService.init();
//        } catch (Exception e) {
//            // Log an error if the file is missing or something goes wrong
//            log.error("Failed to import Excel file: {}", e.getMessage(), e);
//        }
//    }
// }
