package za.co.discovery.health.journey.service.journey.processor;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyCategoryRepository;

@Component
@RequiredArgsConstructor
public class JourneyCategoryService {
    private final ExtendedJourneyCategoryRepository categoryRepository;

    public JourneyCategory findByCode(final String categoryCode) {
        return categoryRepository
                .findByCategoryCode(categoryCode)
                .orElseThrow(() -> new CoreException(
                        ReasonCode.VALIDATION_ERROR, "Category not found with code: " + categoryCode));
    }

    public JourneyCategory findByNameAndType(final String journeyName, final String type) {
        return categoryRepository
                .findByNameAndType(journeyName, type)
                .orElseThrow(() ->
                        new CoreException(ReasonCode.VALIDATION_ERROR, "Category not found with name: " + journeyName));
    }
}
