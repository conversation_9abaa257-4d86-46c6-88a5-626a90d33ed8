package za.co.discovery.health.journey.service.dataattr;

import com.vitality.journey.importer.domain.MemberDto;
import com.vitality.journey.importer.domain.MemberJourneyTemplateDto;
import com.vitality.journey.importer.domain.MemberProgramDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import za.co.discovery.health.dataattribute.api.DataControllerApi;
import za.co.discovery.health.dataattribute.model.MemberAttribute;
import za.co.discovery.health.dataattribute.model.MemberAttributesChanged;

import java.time.ZoneId;
import java.util.Date;
import java.util.List;

import static java.util.Objects.requireNonNull;

@Component
@RequiredArgsConstructor
public class DataAttributesProcessor {
    private static final String DPP_MIGRATION_SOURCE = "DPPMigration";

    private static final String DPP_STARTING_WEIGHT = "DPPStartingWeight";
    private static final String DPP_TARGET_WEIGHT_LOSS = "DPPTargetWeightLoss";

    private final DataControllerApi dataControllerApi;

    public void process(final MemberJourneyTemplateDto journeyTemplate) {
        MemberProgramDto program = requireNonNull(journeyTemplate.getProgram());
        MemberDto member = requireNonNull(journeyTemplate.getMember());

        Date captureDateTime = Date.from(requireNonNull(program.getProgramStartDate())
            .atZone(ZoneId.systemDefault()).toInstant());

        MemberAttribute startingDppWeightAttr = new MemberAttribute();
        startingDppWeightAttr.memberAttributeName(DPP_STARTING_WEIGHT);
        startingDppWeightAttr.setSource(DPP_MIGRATION_SOURCE);
        startingDppWeightAttr.setCapturedAt(captureDateTime);
        startingDppWeightAttr.setMemberAttributeValue(String.valueOf(program.getStartingWeight()));

        MemberAttribute targetDppWeightLossAttr = new MemberAttribute();
        targetDppWeightLossAttr.memberAttributeName(DPP_TARGET_WEIGHT_LOSS);
        targetDppWeightLossAttr.setSource(DPP_MIGRATION_SOURCE);
        targetDppWeightLossAttr.setCapturedAt(captureDateTime);
        targetDppWeightLossAttr.setMemberAttributeValue(String.valueOf(program.getTargetWeight()));

        MemberAttributesChanged memberAttributesChanged = new MemberAttributesChanged();
        memberAttributesChanged.setEntityNo(member.getEntityNo());
        memberAttributesChanged.setSyncToCore(true);
        memberAttributesChanged.setMemberAttributes(List.of(startingDppWeightAttr, targetDppWeightLossAttr));

        dataControllerApi.updateDemographicFactsForEntity(memberAttributesChanged).block();
    }
}
