package za.co.discovery.health.journey.resolver.rule.model.output;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import za.co.discovery.health.journey.util.DMNConstants;
import za.co.discovery.health.journey.util.DMNUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Getter
@Builder(access = AccessLevel.PRIVATE)
public class ActivityDMNRuleOutput {

    private final List<DMNOutput> outputs;

    /**
     * Creates an {@link ActivityDMNRuleOutput} from a list of result maps.
     * Each map is expected to contain keys defined in {@link DMNConstants}.
     *
     * @param result List of maps containing DMN rule output data
     * @return An instance of {@link ActivityDMNRuleOutput}, or an empty one if invalid input
     */
    public static ActivityDMNRuleOutput of(final List<Map<String, Object>> result) {
        // No match found or timePeriod not configured
        if (result == null || result.isEmpty()) {
            return ActivityDMNRuleOutput.builder()
                    .outputs(Collections.emptyList())
                    .build();
        }

        final List<DMNOutput> outputs = result.stream()
                .map(ActivityDMNRuleOutput::convertToDMNOutput)
                .filter(ActivityDMNRuleOutput::hasValidMnemonic)
                .collect(Collectors.toList());

        return ActivityDMNRuleOutput.builder().outputs(outputs).build();
    }

    /**
     * Converts a single map of DMN data to a {@link DMNOutput} object.
     *
     * @param activityMap Map containing activity data
     * @return {@link DMNOutput} built from the map; never null (fields may be null)
     */
    private static DMNOutput convertToDMNOutput(final Map<String, Object> activityMap) {
        // Safely convert map values to the correct data types
        final String mnemonic = DMNUtils.toStringOrNull(activityMap.get(DMNConstants.ACTIVITY_ID));
        final Long count = DMNUtils.toLongOrNull(activityMap.get(DMNConstants.FREQUENCY));
        final String flexibility = DMNUtils.toStringOrNull(activityMap.get(DMNConstants.COMPLETION_TYPE));

        final String name = DMNUtils.toStringOrNull(activityMap.get(DMNConstants.ACTIVITY_NAME));
        final String icon = DMNUtils.toStringOrNull(activityMap.get(DMNConstants.ACTIVITY_ICON));
        final String type = DMNUtils.toStringOrNull(activityMap.get(DMNConstants.ACTIVITY_TYPE));
        final String skipMilestone = DMNUtils.toStringOrNull(activityMap.get(DMNConstants.IS_SKIP_MILESTONE));

        return DMNOutput.builder()
                .mnemonic(mnemonic)
                .type(type)
                .name(name)
                .icon(icon)
                .count(count)
                .flexibility(flexibility)
                .isSkipPeriod(Boolean.valueOf(skipMilestone))
                .build();
    }

    /**
     * Checks if the DMNOutput has a non-null mnemonic.
     * Logs an error if the mnemonic is null.
     *
     * @param output DMNOutput to check
     * @return true if mnemonic is not null, false otherwise
     */
    private static boolean hasValidMnemonic(final DMNOutput output) {
        if (Objects.isNull(output.getMnemonic()) && !output.isSkipPeriod) {
            log.error("Mnemonic is null. Skipping DMNOutput: {}", output);
            return false;
        }
        return true;
    }

    @Data
    @Builder
    public static class DMNOutput {
        private final String mnemonic;
        private final String type;
        private final String name;
        private final String icon;
        private final Long count;
        private final String flexibility;
        private final Boolean isSkipPeriod;
    }
}
