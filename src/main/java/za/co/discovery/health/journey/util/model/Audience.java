package za.co.discovery.health.journey.util.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Objects;

@SuppressWarnings("PMD.DataClass")
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class Audience {
    private String allianceNo;
    private String branchNo;
    private String groupNo;
    private String role;

    public String getRole() {
        return Objects.requireNonNullElse(role, "*");
    }

    public String getGroupNo() {
        return Objects.requireNonNullElse(groupNo, "*");
    }

    public String getBranchNo() {
        return Objects.requireNonNullElse(branchNo, "*");
    }

    public String getAllianceNo() {
        return Objects.requireNonNullElse(allianceNo, "*");
    }
}
