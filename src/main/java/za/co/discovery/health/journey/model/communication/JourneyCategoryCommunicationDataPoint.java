package za.co.discovery.health.journey.model.communication;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import za.co.discovery.health.journey.constant.JourneyCommunicationConstant;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class JourneyCategoryCommunicationDataPoint {
    private Long categoryId;
    private String journeyCategoryType;
    private Map<JourneyCommunicationConstant, String> attributes;
    private List<JourneyProgramCommunicationDataPoint> programCommunicationDataPoints;
}
