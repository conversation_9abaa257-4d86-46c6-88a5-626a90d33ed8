package za.co.discovery.health.journey.resolver.rule.model.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;
import za.co.discovery.health.journey.resolver.rule.model.Flexibility;
import za.co.discovery.health.journey.resolver.rule.model.RuleResult;
import za.co.discovery.health.journey.resolver.rule.model.evaluator.ActivityRuleEvaluationResult;

import java.util.List;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class GetActivityRuleResult extends RuleResult {
    private final List<RecommendedActivities> activities;
    private final boolean skipPeriod;

    @Data
    @Builder
    @AllArgsConstructor
    public static class RecommendedActivities {
        private final String mnemonic;
        private final ActivityDetailsType type;
        private final String name;
        private final String icon;
        private final Long count;
        private final Flexibility flexibility;

        public static RecommendedActivities of(final ActivityRuleEvaluationResult.RecommendedActivityResult output) {
            return RecommendedActivities.builder()
                    .mnemonic(output.getMnemonic())
                    .name(output.getName())
                    .type(output.getType())
                    .icon(output.getIcon())
                    .count(output.getCount())
                    .flexibility(output.getFlexibility())
                    .build();
        }
    }
}
