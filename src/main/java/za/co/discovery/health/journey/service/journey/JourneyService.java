package za.co.discovery.health.journey.service.journey;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.resolver.rule.impl.RuleCalculatorResolver;
import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;
import za.co.discovery.health.journey.resolver.rule.model.RuleRequest;
import za.co.discovery.health.journey.resolver.rule.model.RuleType;
import za.co.discovery.health.journey.resolver.rule.model.result.CategoryConfigurationRuleResult;
import za.co.discovery.health.journey.resolver.rule.model.result.JourneyActivationRuleResult;

import java.util.List;

@Service
@RequiredArgsConstructor
public class JourneyService {

    private final RuleCalculatorResolver ruleCalculatorResolver;

    @Transactional(readOnly = true)
    public List<JourneyActivationRuleResult.CategoryRecommendation> getRecommendedCategories(final Long entityId) {
        final JourneyActivationRuleResult result = (JourneyActivationRuleResult) ruleCalculatorResolver
                .getEvaluator(RuleType.JOURNEY_ACTIVATION)
                .calculate(
                        RuleType.JOURNEY_ACTIVATION,
                        RuleRequest.builder()
                                .entityId(entityId)
                                .ruleName("JOURNEY ACTIVATION")
                                .processorType(RuleProcessorType.DMN)
                                .build());

        return result.getCategories();
    }

    @Transactional(readOnly = true)
    public CategoryConfigurationRuleResult.CategoryConfiguration getCategoryConfiguration(
            final Long entityId, final JourneyCategory journeyCategory) {
        if (journeyCategory.getJourneyRules() == null
                || journeyCategory.getJourneyRules().getRuleSetName() == null
                || journeyCategory.getJourneyRules().getRuleSetType() == null) {
            return CategoryConfigurationRuleResult.CategoryConfiguration.builder()
                    .enrollmentStartTime(null)
                    .enrollmentEndTime(null)
                    .journeyStartTime(null)
                    .activityPrecondition(null)
                    .enrollmentPrecondition(null)
                    .maxParticipants(null)
                    .monitoringPeriodDuration(null)
                    .build();
        }
        final CategoryConfigurationRuleResult result = (CategoryConfigurationRuleResult) ruleCalculatorResolver
                .getEvaluator(RuleType.CATEGORY_CONFIGURATION_RULE)
                .calculate(
                        RuleType.CATEGORY_CONFIGURATION_RULE,
                        RuleRequest.builder()
                                .entityId(entityId)
                                .ruleName(journeyCategory.getJourneyRules().getRuleSetName())
                                .processorType(RuleProcessorType.valueOf(
                                        journeyCategory.getJourneyRules().getRuleSetType()))
                                .build());
        return result.getConfiguration();
    }
}
