package za.co.discovery.health.journey.service.bo.dmn;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.model.bo.dmn.ActivityRecommendationDmnRuleDto;
import za.co.discovery.health.journey.model.bo.dmn.CategoryConfigurationDmnRuleDto;
import za.co.discovery.health.journey.model.bo.dmn.DmnTableRow;
import za.co.discovery.health.journey.model.bo.dmn.JourneyActivationDmnDto;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class JourneyDmnMapper {

    private final DmnTableExtractor baseExtractor;
    private final JourneyActivationDmnRuleMapper journeyActivationDmnRuleMapper;
    private final CategoryConfigurationDmnRuleMapper categoryConfigurationDmnRuleMapper;
    private final ActivityRecommendationDmnRuleMapper activityRecommendationDmnRuleMapper;

    public List<JourneyActivationDmnDto> getJourneyActivation() {
        final List<DmnTableRow> journeyDecisions = baseExtractor.extractTableRows("JOURNEY ACTIVATION", "decision");

        return journeyActivationDmnRuleMapper.toCustomRules(journeyDecisions);
    }

    public List<ActivityRecommendationDmnRuleDto> getActivityRecommendations(final String ruleName) {
        return activityRecommendationDmnRuleMapper.toActivityRules(
                baseExtractor.extractTableRows(ruleName, "decision"));
    }

    public List<ActivityRecommendationDmnRuleDto> getActivityRecommendations(
            final String ruleName, final ActivityRecommendationDmnRuleDto.ActivityType activityType) {
        return getActivityRecommendations(ruleName).stream()
                .filter(rule -> rule.getActivityType() == activityType)
                .collect(Collectors.toList());
    }

    public List<CategoryConfigurationDmnRuleDto> getCategoryConfigurations(final String ruleName) {
        final List<DmnTableRow> decision = baseExtractor.extractTableRows(ruleName, "decision");
        return categoryConfigurationDmnRuleMapper.toCategoryConfigurationRule(decision);
    }
}
