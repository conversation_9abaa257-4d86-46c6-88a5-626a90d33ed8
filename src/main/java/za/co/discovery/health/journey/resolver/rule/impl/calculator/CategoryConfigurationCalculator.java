package za.co.discovery.health.journey.resolver.rule.impl.calculator;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import za.co.discovery.health.journey.resolver.rule.RuleCalculator;
import za.co.discovery.health.journey.resolver.rule.impl.RuleEvaluatorResolver;
import za.co.discovery.health.journey.resolver.rule.model.RuleRequest;
import za.co.discovery.health.journey.resolver.rule.model.RuleType;
import za.co.discovery.health.journey.resolver.rule.model.evaluator.CategoryConfigurationEvaluationResult;
import za.co.discovery.health.journey.resolver.rule.model.result.CategoryConfigurationRuleResult;
import za.co.discovery.health.journey.rule.cache.RuleCacheService;
import za.co.discovery.health.journey.rule.cache.model.RuleCache;

import java.util.Map;

@Component
@RequiredArgsConstructor
public class CategoryConfigurationCalculator implements RuleCalculator {
    private final RuleCacheService cacheService;
    private final RuleEvaluatorResolver evaluator;

    @Override
    public RuleType getHandledRuleType() {
        return RuleType.CATEGORY_CONFIGURATION_RULE;
    }

    @Override
    public CategoryConfigurationRuleResult calculate(final RuleType type, final RuleRequest ruleRequest) {
        final RuleCache ruleCache = cacheService.getRuleCache(ruleRequest.getRuleName());

        // PREPARE VARIABLES
        final CategoryConfigurationEvaluationResult evaluate = (CategoryConfigurationEvaluationResult) evaluator
                .getEvaluator(type, ruleCache.getProcessorType())
                .evaluate(ruleRequest.getEntityId(), ruleCache, Map.of());

        if (evaluate.isSuccess()) {
            return CategoryConfigurationRuleResult.builder()
                    .configuration(CategoryConfigurationRuleResult.CategoryConfiguration.of(evaluate.getResult()))
                    .ruleType(ruleCache.getProcessorType())
                    .success(true)
                    .build();
        } else {
            return CategoryConfigurationRuleResult.builder()
                    .configuration(null)
                    .ruleType(ruleCache.getProcessorType())
                    .success(false)
                    .build();
        }
    }
}
