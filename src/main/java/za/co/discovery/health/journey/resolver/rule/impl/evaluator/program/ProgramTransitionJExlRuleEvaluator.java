package za.co.discovery.health.journey.resolver.rule.impl.evaluator.program;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.resolver.rule.RuleEvaluator;
import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;
import za.co.discovery.health.journey.resolver.rule.model.RuleType;
import za.co.discovery.health.journey.resolver.rule.model.evaluator.ProgramTransitionRuleEvaluationResult;
import za.co.discovery.health.journey.rule.cache.model.RuleCache;
import za.co.discovery.health.journey.rule.conversion.ConfigFlagJexlRunner;

import java.util.Map;

@Service
@RequiredArgsConstructor
public class ProgramTransitionJExlRuleEvaluator implements RuleEvaluator {
    private final ConfigFlagJexlRunner runner;

    @Override
    public RuleType getHandledRuleType() {
        return RuleType.PROGRAM_COMPLETION_RULE;
    }

    @Override
    public RuleProcessorType getHandledProcessorType() {
        return RuleProcessorType.JEXL;
    }

    @SuppressWarnings("PMD.AvoidCatchingGenericException")
    @Override
    public ProgramTransitionRuleEvaluationResult evaluate(
            final Long entityId, final RuleCache cache, final Map<String, Object> vars) {
        try {
            return ProgramTransitionRuleEvaluationResult.builder()
                    .success(true)
                    .canTransition(runner.run(entityId, cache.getRuleContent(), vars))
                    .build();
        } catch (Exception e) {
            return ProgramTransitionRuleEvaluationResult.builder()
                    .success(false)
                    .canTransition(false)
                    .build();
        }
    }
}
