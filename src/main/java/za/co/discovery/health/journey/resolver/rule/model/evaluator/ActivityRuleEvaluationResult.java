package za.co.discovery.health.journey.resolver.rule.model.evaluator;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;
import org.springframework.util.StringUtils;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;
import za.co.discovery.health.journey.resolver.rule.model.Flexibility;
import za.co.discovery.health.journey.resolver.rule.model.output.ActivityDMNRuleOutput;

import java.util.List;
import java.util.stream.Collectors;

@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
public class ActivityRuleEvaluationResult extends RuleEvaluationResult {
    private List<RecommendedActivityResult> results;
    private boolean skipPeriod;

    public static ActivityRuleEvaluationResult of(final ActivityDMNRuleOutput results, final boolean success) {

        if (success) {
            return ActivityRuleEvaluationResult.builder()
                    .results(results.getOutputs().stream()
                            .map(RecommendedActivityResult::of)
                            .collect(Collectors.toList()))
                    .success(true)
                    .build();
        } else {
            return ActivityRuleEvaluationResult.builder()
                    .results(List.of())
                    .success(false)
                    .build();
        }
    }

    @Data
    @SuperBuilder(toBuilder = true)
    public static class RecommendedActivityResult {
        private String mnemonic;
        private ActivityDetailsType type;
        private String name;
        private String icon;
        private Long count;
        private Flexibility flexibility;
        private boolean skipPeriod;

        public static RecommendedActivityResult of(final ActivityDMNRuleOutput.DMNOutput output) {
            return RecommendedActivityResult.builder()
                    .mnemonic(output.getMnemonic())
                    .type(ActivityDetailsType.fromValue(output.getType()))
                    .name(output.getName())
                    .icon(output.getIcon())
                    .count(output.getCount())
                    .flexibility(
                            StringUtils.hasText(output.getFlexibility())
                                    ? Flexibility.fromValue(output.getFlexibility())
                                    : Flexibility.OPTIONAL)
                    .skipPeriod(output.getIsSkipPeriod())
                    .build();
        }
    }
}
