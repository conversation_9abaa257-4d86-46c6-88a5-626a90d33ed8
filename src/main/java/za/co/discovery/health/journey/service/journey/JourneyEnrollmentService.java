package za.co.discovery.health.journey.service.journey;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentAttribute;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestone;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgram;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgramBehaviour;
import za.co.discovery.health.journey.database.databaseMapping.JourneyRules;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentMilestoneRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentRepository;
import za.co.discovery.health.journey.mapper.JourneyEnrollmentMapper;
import za.co.discovery.health.journey.model.enums.EnrollmentStatus;
import za.co.discovery.health.journey.model.enums.UserJourneyState;
import za.co.discovery.health.journey.model.user.CurrentUserMilestoneEnrollmentDto;
import za.co.discovery.health.journey.model.user.LimitedUserCategoryEnrollmentDto;
import za.co.discovery.health.journey.model.user.UserCategoryEnrollmentDto;
import za.co.discovery.health.journey.model.user.UserProgramEnrollmentDto;
import za.co.discovery.health.journey.resolver.rule.impl.RuleCalculatorResolver;
import za.co.discovery.health.journey.resolver.rule.model.RuleProcessorType;
import za.co.discovery.health.journey.resolver.rule.model.RuleType;
import za.co.discovery.health.journey.resolver.rule.model.request.ProgramTransitionRuleRequest;
import za.co.discovery.health.journey.resolver.rule.model.result.GetProgramTransitionRuleResult;
import za.co.discovery.health.journey.service.journey.reward.JourneyEnrollmentRewardService;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@SuppressWarnings("PMD.ExcessiveImports")
public class JourneyEnrollmentService {
    private final JourneyEnrollmentMapper mapper;
    private final RuleCalculatorResolver resolver;
    private final JourneyEnrollmentRewardService rewardService;
    private final ExtendedJourneyEnrollmentRepository journeyEnrollmentRepository;
    private final ExtendedJourneyEnrollmentMilestoneRepository journeyEnrollmentMilestoneRepository;

    private static final int PAGE_SIZE = 100;

    @Transactional
    public void deleteEnrollmentsWithAttribute(final String attrName, final String attrValue) {
        int totalDeleted = 0;
        long lastId = 0L;

        // delete enrollment in batches to avoid high memory consumption
        List<Long> ids;
        do {
            ids = journeyEnrollmentRepository.findNextEnrollmentIdsWithAttribute(
                    attrName, attrValue, lastId, PageRequest.of(0, PAGE_SIZE));

            if (!ids.isEmpty()) {
                final List<JourneyEnrollment> entities = journeyEnrollmentRepository.findAllById(ids);
                journeyEnrollmentRepository.deleteAll(entities);
                journeyEnrollmentRepository.flush();

                totalDeleted += entities.size();
                lastId = ids.get(ids.size() - 1);
            }
        } while (!ids.isEmpty());

        log.info("Deleted {} enrollments with attribute: {} = {}", totalDeleted, attrName, attrValue);
    }

    @Transactional
    public int deleteUserEnrollments(final long userId, final long journeyCategoryId) {
        final List<Long> ids = journeyEnrollmentRepository.findEnrollmentIds(userId, journeyCategoryId);

        if (!ids.isEmpty()) {
            log.info("Deleting {} enrollments for journey: {}, user: {}", ids.size(), journeyCategoryId, userId);
            journeyEnrollmentRepository.deleteAllById(ids);
        }

        return ids.size();
    }

    @Transactional(readOnly = true)
    public Optional<UserCategoryEnrollmentDto> getCategoryEnrollments(
            final Long categoryId, final Long memberId, final LocalDateTime time) {

        validateInputs(memberId, time);
        Assert.notNull(categoryId, "Category ID cannot be null");

        return journeyEnrollmentRepository.findEnrollments(memberId, categoryId).stream()
                .collect(Collectors.groupingBy(JourneyEnrollment::getJourneyCategory))
                .entrySet()
                .stream()
                .map(entrySet -> buildDetailedCategoryDto(entrySet.getKey(), entrySet.getValue(), memberId, time))
                .findFirst();
    }

    @Transactional(readOnly = true)
    public Long countMembersInCategory(final Long categoryId) {

        Assert.notNull(categoryId, "Category ID cannot be null");

        log.info("Counting members in category: {}", categoryId);
        return journeyEnrollmentRepository.countMembersInCategory(categoryId);
    }

    @Transactional(readOnly = true)
    public List<UserCategoryEnrollmentDto> getDetailedEnrollments(final Long memberId, final LocalDateTime time) {
        log.info("Getting detailed enrollments for member: {}", memberId);
        validateInputs(memberId, time);

        return buildCategoryEnrollments(
                memberId,
                time,
                (id, t) -> journeyEnrollmentRepository.findEnrollments(id),
                this::buildDetailedCategoryDto);
    }

    @Transactional(readOnly = true)
    public List<LimitedUserCategoryEnrollmentDto> getLimitedEnrollments(final Long memberId, final LocalDateTime time) {
        log.info("Getting limited category details for member: {}", memberId);
        validateInputs(memberId, time);

        return buildCategoryEnrollments(
                memberId,
                time,
                (id, t) -> journeyEnrollmentRepository.findEnrollments(id),
                this::buildLimitedCategoryDto);
    }

    @Transactional(readOnly = true)
    public List<LimitedUserCategoryEnrollmentDto> getActiveLimitedEnrollments(
            final Long memberId, final LocalDateTime time) {
        log.info("Getting current category details for member: {}", memberId);
        validateInputs(memberId, time);

        final var activeCategoryEnrollments = buildCategoryEnrollments(
                memberId, time, journeyEnrollmentRepository::findCurrentEnrollments, this::buildLimitedCategoryDto);

        final var monitoringPeriodEnrollments = buildCategoryEnrollments(
                memberId,
                time,
                journeyEnrollmentRepository::findEnrollmentsInMonitoringPeriod,
                this::buildLimitedCategoryDto);

        activeCategoryEnrollments.addAll(monitoringPeriodEnrollments);

        return activeCategoryEnrollments;
    }

    private void validateInputs(final Long memberId, final LocalDateTime time) {
        Assert.notNull(memberId, "Member ID cannot be null");
        Assert.notNull(time, "Time cannot be null");
    }

    private void validateInputs(final Long userId, final Long journeyCategoryId) {
        Assert.notNull(userId, "User ID cannot be null");
        Assert.notNull(journeyCategoryId, "Journey category ID cannot be null");
    }

    private <T> List<T> buildCategoryEnrollments(
            final Long memberId,
            final LocalDateTime time,
            final EnrollmentFetcher enrollmentFetcher,
            final CategoryDtoBuilder<T> dtoBuilder) {

        return enrollmentFetcher.apply(memberId, time).stream()
                .collect(Collectors.groupingBy(JourneyEnrollment::getJourneyCategory))
                .entrySet()
                .stream()
                .map(entrySet -> dtoBuilder.build(entrySet.getKey(), entrySet.getValue(), memberId, time))
                .collect(Collectors.toList());
    }

    private UserCategoryEnrollmentDto buildDetailedCategoryDto(
            final JourneyCategory category,
            final List<JourneyEnrollment> enrollments,
            final Long memberId,
            final LocalDateTime time) {

        final List<UserProgramEnrollmentDto> userEnrollments = mapper.getUserEnrollments(enrollments, time);
        final JourneyEnrollment currentEnrollment = journeyEnrollmentRepository
                .findCurrentEnrollment(category.getJourneyCategoryId(), memberId, time)
                .orElse(enrollments.get(enrollments.size() - 1));

        final long totalJourneyDuration = enrollments.stream()
                .collect(Collectors.summarizingLong(it ->
                        it.getJourneyProgram().getJourneyProgramBehaviour().getProgramDuration()))
                .getSum();

        final String durationUnitStr = enrollments.stream()
                .map(it -> it.getJourneyProgram()
                        .getJourneyProgramBehaviour()
                        .getJourneyMilestone()
                        .getDescription())
                .findFirst()
                .orElseThrow(() -> new CoreException(
                        ReasonCode.VALIDATION_ERROR,
                        "No duration unit found for category: " + category.getCategoryCode()));

        return UserCategoryEnrollmentDto.builder()
                .categoryId(category.getJourneyCategoryId())
                .state(determineState(userEnrollments))
                .categoryName(category.getName())
                .categoryType(category.getJourneyCategoryType().getName())
                .journeyPrograms(userEnrollments)
                .externalReference(category.getExternalReference())
                .enrollmentTime(currentEnrollment.getEnrollmentDate())
                .startTime(getFirstMilestone(currentEnrollment.getJourneyEnrollmentId()))
                .journeyDuration(totalJourneyDuration)
                .journeyDurationUnit(ChronoUnit.valueOf(durationUnitStr.toUpperCase(Locale.US)))
                .currentMilestone(getCurrentMilestone(category.getJourneyCategoryId(), memberId, time))
                .monitoringPeriodEndTime(currentEnrollment.getMonitoringPeriodEndDate())
                .build();
    }

    private UserJourneyState determineState(final List<UserProgramEnrollmentDto> userEnrollments) {
        final boolean hasActive =
                userEnrollments.stream().anyMatch(enrollment -> EnrollmentStatus.ACTIVE.equals(enrollment.getStatus()));
        final boolean hasCompleted = userEnrollments.stream()
                .anyMatch(enrollment -> EnrollmentStatus.COMPLETED.equals(enrollment.getStatus()));
        final boolean hasNotAchieved = userEnrollments.stream()
                .anyMatch(enrollment -> EnrollmentStatus.NOT_ACHIEVED.equals(enrollment.getStatus()));

        if (hasActive) {
            return UserJourneyState.ENROLLED;
        } else if (hasCompleted) {
            return UserJourneyState.COMPLETED;
        } else if (hasNotAchieved) {
            return UserJourneyState.NOT_ACHIEVED;
        } else {
            return UserJourneyState.ENROLLED;
        }
    }

    @SuppressWarnings("PMD.UnusedFormalParameter")
    private LimitedUserCategoryEnrollmentDto buildLimitedCategoryDto(
            final JourneyCategory category,
            final List<JourneyEnrollment> enrollments,
            final Long memberId,
            final LocalDateTime time) {

        final long totalJourneyDuration = enrollments.stream()
                .collect(Collectors.summarizingLong(it ->
                        it.getJourneyProgram().getJourneyProgramBehaviour().getProgramDuration()))
                .getSum();

        final String durationUnitStr = enrollments.stream()
                .map(it -> it.getJourneyProgram()
                        .getJourneyProgramBehaviour()
                        .getJourneyMilestone()
                        .getDescription())
                .findFirst()
                .orElseThrow(() -> new CoreException(
                        ReasonCode.VALIDATION_ERROR,
                        "No duration unit found for category: " + category.getCategoryCode()));

        final JourneyEnrollment currentEnrollment = journeyEnrollmentRepository
                .findCurrentEnrollment(category.getJourneyCategoryId(), memberId, time)
                .orElse(enrollments.get(enrollments.size() - 1));

        return LimitedUserCategoryEnrollmentDto.builder()
                .categoryId(category.getJourneyCategoryId())
                .state(determineState(mapper.getUserEnrollments(enrollments, time)))
                .categoryName(category.getName())
                .categoryType(category.getJourneyCategoryType().getName())
                .enrollmentTime(currentEnrollment.getEnrollmentDate())
                .startTime(getFirstMilestone(currentEnrollment.getJourneyEnrollmentId()))
                .journeyDuration(totalJourneyDuration)
                .journeyDurationUnit(ChronoUnit.valueOf(durationUnitStr.toUpperCase(Locale.US)))
                .currentMilestone(getCurrentMilestone(category.getJourneyCategoryId(), memberId, time))
                .monitoringPeriodEndTime(currentEnrollment.getMonitoringPeriodEndDate())
                .build();
    }

    private CurrentUserMilestoneEnrollmentDto getCurrentMilestone(
            final Long categoryId, final Long memberId, final LocalDateTime time) {
        return mapper.getCurrentUserMilestoneEnrollmentDto(
                journeyEnrollmentMilestoneRepository.findMilestoneForCategory(categoryId, memberId, time), time);
    }

    private LocalDateTime getFirstMilestone(final Long journeyEnrollmentId) {
        return journeyEnrollmentMilestoneRepository.getMilestonesByEnrollment(journeyEnrollmentId).stream()
                .findFirst()
                .map(JourneyEnrollmentMilestone::getMilestoneFrom)
                .orElse(LocalDateTime.now());
    }

    public JourneyEnrollment getById(final Long enrollmentId) {
        Assert.notNull(enrollmentId, "Enrollment ID cannot be null");

        return journeyEnrollmentRepository
                .findById(enrollmentId)
                .orElseThrow(
                        () -> new CoreException(ReasonCode.VALIDATION_ERROR, "Enrollment not found: " + enrollmentId));
    }

    public boolean isProgramComplete(final Long enrollmentId) {
        final JourneyEnrollment enrollment = getById(enrollmentId);
        final JourneyProgramBehaviour journeyProgramBehaviour =
                enrollment.getJourneyProgram().getJourneyProgramBehaviour();
        final JourneyRules rule = journeyProgramBehaviour.getJourneyRulesByProgramCompletionRulesId();

        final GetProgramTransitionRuleResult result = evaluateProgramCompletion(enrollment, rule);

        if (!result.isSuccess()) {
            throw new CoreException(
                    ReasonCode.EVALUATION_ERROR,
                    "Failed to calculate program completion for enrollment: " + enrollmentId);
        }

        return result.isCanTransition();
    }

    private GetProgramTransitionRuleResult evaluateProgramCompletion(
            final JourneyEnrollment enrollment, final JourneyRules rule) {

        return (GetProgramTransitionRuleResult) resolver.getEvaluator(RuleType.PROGRAM_COMPLETION_RULE)
                .calculate(
                        RuleType.PROGRAM_COMPLETION_RULE,
                        ProgramTransitionRuleRequest.builder()
                                .programDuration(enrollment
                                        .getJourneyProgram()
                                        .getJourneyProgramBehaviour()
                                        .getProgramDuration())
                                .milestones(enrollment.getJourneyEnrollmentMilestones().stream()
                                        .map(milestone -> ProgramTransitionRuleRequest.MilestoneDetails.builder()
                                                .iteration(milestone.getMilestoneIteration())
                                                .status(milestone.getMilestoneStatus())
                                                .activities(milestone.getJourneyEnrollmentMilestoneActivities().stream()
                                                        .map(activity ->
                                                                ProgramTransitionRuleRequest.ActivityDetails.builder()
                                                                        .mnemonic(activity.getActivityMnemonicId())
                                                                        .activityType(activity.getActivityType())
                                                                        .status(activity.getActivityStatus())
                                                                        .build())
                                                        .collect(Collectors.toList()))
                                                .build())
                                        .collect(Collectors.toList()))
                                .ruleName(rule.getRuleSetName())
                                .processorType(RuleProcessorType.valueOf(rule.getRuleSetType()))
                                .entityId(enrollment.getEntityId())
                                .build());
    }

    public List<JourneyEnrollment> findEnrollments(final Long userId, final Long journeyCategoryId) {
        validateInputs(userId, journeyCategoryId);
        return journeyEnrollmentRepository.findEnrollments(userId, journeyCategoryId);
    }

    @Transactional
    public JourneyEnrollment createEnrollment(
            final Long userId,
            final JourneyProgram firstProgram,
            final JourneyCategory category,
            final LocalDateTime now) {

        return createEnrollment(userId, firstProgram, category, now, Map.of());
    }

    @Transactional
    public JourneyEnrollment createEnrollment(
            final Long userId,
            final JourneyProgram firstProgram,
            final JourneyCategory category,
            final LocalDateTime now,
            final Map<String, String> attributes) {

        validateCreateEnrollmentInputs(userId, firstProgram, category, now);

        final JourneyEnrollment enrollment = new JourneyEnrollment();
        enrollment.setEntityId(userId);
        enrollment.setJourneyCategory(category);
        enrollment.setJourneyProgram(firstProgram);
        enrollment.setEnrollmentDate(now);
        enrollment.setStatus(EnrollmentStatus.ACTIVE.getValue());

        addAttributes(enrollment, attributes);

        log.info(
                "Creating new enrollment for user {} in category {} with program {}",
                userId,
                category.getName(),
                firstProgram.getName());

        return journeyEnrollmentRepository.save(enrollment);
    }

    private static void addAttributes(final JourneyEnrollment enrollment, final Map<String, String> attributes) {
        if (attributes != null && !attributes.isEmpty()) {
            attributes.forEach((key, value) -> {
                final JourneyEnrollmentAttribute attribute = new JourneyEnrollmentAttribute();
                attribute.setJourneyEnrollment(enrollment);
                attribute.setAttrName(key);
                attribute.setAttrValue(value);
                enrollment.getJourneyEnrollmentAttributes().add(attribute);
            });
        }
    }

    private void validateCreateEnrollmentInputs(
            final Long userId,
            final JourneyProgram firstProgram,
            final JourneyCategory category,
            final LocalDateTime now) {

        Assert.notNull(userId, "User ID cannot be null");
        Assert.notNull(firstProgram, "Journey program cannot be null");
        Assert.notNull(category, "Journey category cannot be null");
        Assert.notNull(now, "Enrollment date cannot be null");
    }

    public void markCompleted(
            final Long journeyEnrollmentId, final LocalDateTime now, final Duration monitoringPeriodDuration) {
        markCompleted(journeyEnrollmentId, null, now, monitoringPeriodDuration);
    }

    public void markCompleted(
            final Long journeyEnrollmentId,
            final JourneyEnrollment newEnrollment,
            final LocalDateTime now,
            final Duration monitoringPeriodDuration) {

        Assert.notNull(journeyEnrollmentId, "Journey enrollment ID cannot be null");
        Assert.notNull(now, "Completion time cannot be null");

        journeyEnrollmentRepository
                .findById(journeyEnrollmentId)
                .map(enrollment -> {
                    enrollment.setStatus(EnrollmentStatus.COMPLETED.getValue());
                    enrollment.setTerminationDate(now);
                    enrollment.setJourneyEnrollment(newEnrollment);
                    enrollment.setMonitoringPeriodEndDate(
                            Objects.nonNull(monitoringPeriodDuration)
                                    ? now.plus(monitoringPeriodDuration)
                                    : LocalDateTime.now());

                    log.info(
                            "Marking enrollment {} as completed for user {}",
                            journeyEnrollmentId,
                            enrollment.getEntityId());

                    return journeyEnrollmentRepository.save(enrollment);
                })
                .ifPresent(enrollment -> rewardService.assignProgramReward(enrollment, now));
    }

    public void markLateCompleted(
            final Long journeyEnrollmentId, final LocalDateTime now, final Duration monitoringPeriodDuration) {
        markLateCompleted(journeyEnrollmentId, null, now, monitoringPeriodDuration);
    }

    public void markLateCompleted(
            final Long journeyEnrollmentId,
            final JourneyEnrollment newEnrollment,
            final LocalDateTime now,
            final Duration monitoringPeriodDuration) {

        Assert.notNull(journeyEnrollmentId, "Journey enrollment ID cannot be null");
        Assert.notNull(now, "Completion time cannot be null");

        journeyEnrollmentRepository.findById(journeyEnrollmentId).map(enrollment -> {
            enrollment.setStatus(EnrollmentStatus.COMPLETED.getValue());
            enrollment.setTerminationDate(now);
            enrollment.setJourneyEnrollment(newEnrollment);
            enrollment.setMonitoringPeriodEndDate(
                    Objects.nonNull(monitoringPeriodDuration)
                            ? now.plus(monitoringPeriodDuration)
                            : LocalDateTime.now());

            log.info(
                    "Marking enrollment {} as late completed for user {}",
                    journeyEnrollmentId,
                    enrollment.getEntityId());

            return journeyEnrollmentRepository.save(enrollment);
        });
    }

    public void markNotAchieved(
            final Long journeyEnrollmentId, final LocalDateTime now, final Duration monitoringPeriodDuration) {
        Assert.notNull(journeyEnrollmentId, "Journey enrollment ID cannot be null");
        Assert.notNull(now, "Completion time cannot be null");

        journeyEnrollmentRepository.findById(journeyEnrollmentId).map(enrollment -> {
            enrollment.setStatus(EnrollmentStatus.NOT_ACHIEVED.getValue());
            enrollment.setTerminationDate(now);
            enrollment.setMonitoringPeriodEndDate(
                    Objects.nonNull(monitoringPeriodDuration)
                            ? now.plus(monitoringPeriodDuration)
                            : LocalDateTime.now());

            log.info(
                    "Marking enrollment {} as not achieved for user {}", journeyEnrollmentId, enrollment.getEntityId());

            return journeyEnrollmentRepository.save(enrollment);
        });
    }

    // Simplified functional interfaces
    @FunctionalInterface
    private interface EnrollmentFetcher {
        List<JourneyEnrollment> apply(Long memberId, LocalDateTime time);
    }

    @FunctionalInterface
    private interface CategoryDtoBuilder<T> {
        T build(JourneyCategory category, List<JourneyEnrollment> enrollments, Long memberId, LocalDateTime time);
    }
}
