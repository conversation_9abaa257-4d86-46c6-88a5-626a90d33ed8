package za.co.discovery.health.journey.config.client;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;
import za.co.discovery.health.dataattribute.ApiClient;
import za.co.discovery.health.dataattribute.api.DataControllerApi;

@Configuration
public class DataAttributeServiceClientConfig {
    private final WebClient webClient;

    @Value("${integration.data-attribute-service.url}")
    private String baseUrl;

    public DataAttributeServiceClientConfig(@Qualifier("b2b-webclient") final WebClient b2bWebClient) {
        this.webClient = b2bWebClient;
    }

    @Bean
    public DataControllerApi dataAttributeServiceControllerApi() {
        final ApiClient apiClient = new ApiClient(webClient);
        apiClient.setBasePath(baseUrl);
        return new Data<PERSON><PERSON>roller<PERSON>pi(apiClient);
    }
}
