package za.co.discovery.health.journey.mapper;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollment;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestone;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestoneActivity;
import za.co.discovery.health.journey.database.databaseMapping.JourneyProgramBehaviour;
import za.co.discovery.health.journey.model.JourneyBehaviourDto;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;
import za.co.discovery.health.journey.model.enums.EnrollmentStatus;
import za.co.discovery.health.journey.model.enums.MilestoneStatus;
import za.co.discovery.health.journey.model.user.CurrentUserMilestoneEnrollmentDto;
import za.co.discovery.health.journey.model.user.UserEnrollmentMilestoneReward;
import za.co.discovery.health.journey.model.user.UserEnrollmentProgramReward;
import za.co.discovery.health.journey.model.user.UserMilestoneEnrollmentDto;
import za.co.discovery.health.journey.model.user.UserProgramEnrollmentDto;
import za.co.discovery.health.journey.resolver.rule.model.Flexibility;
import za.co.discovery.health.journey.service.journey.reward.facade.JourneyProgramRewardFacade;
import za.co.discovery.health.journey.strategy.ActivityCompletionValidator;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;
import static java.util.Comparator.naturalOrder;
import static java.util.Comparator.nullsLast;

@Service
@RequiredArgsConstructor
public class JourneyEnrollmentMapper {
    private final JourneyProgramRewardFacade rewardFacade;
    private final ActivityCompletionValidator activityCompletionValidator;

    public List<UserProgramEnrollmentDto> getUserEnrollments(
            final List<JourneyEnrollment> enrollments, final LocalDateTime time) {
        if (enrollments == null) {
            return Collections.emptyList();
        }
        final AtomicLong index = new AtomicLong(0);

        return enrollments.stream()
                .sorted(comparing(
                        JourneyEnrollment::getTerminationDate,
                        nullsLast(naturalOrder()) // put nulls at the end (ascending order)
                        ))
                .map(enrollment -> {
                    final UserProgramEnrollmentDto dto = getUserEnrollmentDto(enrollment, time);
                    dto.setOrder(index.incrementAndGet());
                    return dto;
                })
                .collect(Collectors.toList());
    }

    public UserProgramEnrollmentDto getUserEnrollmentDto(
            final JourneyEnrollment journeyEnrollment, final LocalDateTime time) {
        final JourneyProgramBehaviour journeyProgramBehaviour =
                journeyEnrollment.getJourneyProgram().getJourneyProgramBehaviour();

        return UserProgramEnrollmentDto.builder()
                .enrollmentId(journeyEnrollment.getJourneyEnrollmentId())
                .programId(journeyEnrollment.getJourneyProgram().getJourneyProgramId())
                .programName(journeyEnrollment.getJourneyProgram().getName())
                .programDescription(journeyEnrollment.getJourneyProgram().getDescription())
                .enrollmentDate(journeyEnrollment.getEnrollmentDate())
                .terminationDate(journeyEnrollment.getTerminationDate())
                .status(calculateEnrollmentStatus(
                        journeyEnrollment.getStatus(), time, journeyEnrollment.getTerminationDate()))
                .milestones(getMilestones(journeyEnrollment, time))
                .behaviour(getJourneyBehaviourDto(journeyProgramBehaviour))
                .rewards(getProgramRewards(journeyEnrollment, time))
                .build();
    }

    @SuppressWarnings("PMD.AvoidDuplicateLiterals")
    private List<UserEnrollmentProgramReward> getProgramRewards(
            final JourneyEnrollment journeyEnrollment, final LocalDateTime time) {
        if ("COMPLETED".equals(journeyEnrollment.getStatus())) {
            return journeyEnrollment.getJourneyEnrollmentProgramAwards().stream()
                    .map(award -> UserEnrollmentProgramReward.builder()
                            .rewardType(award.getRewardType())
                            .rewardValue(award.getRewardValue())
                            .awardedAt(award.getAwardDate())
                            .status(award.getAwardDate() == null ? "PENDING" : "AWARDED")
                            .build())
                    .collect(Collectors.toList());

        } else if ("ACTIVE".equals(journeyEnrollment.getStatus())) {
            return rewardFacade.getProgramRewards(journeyEnrollment, time).stream()
                    .map(reward -> UserEnrollmentProgramReward.builder()
                            .rewardType(reward.getRewardType())
                            .rewardValue(reward.getRewardValue())
                            .awardedAt(null)
                            .status("AVAILABLE")
                            .build())
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @SuppressWarnings("PMD.AvoidLiteralsInIfCondition")
    private EnrollmentStatus calculateEnrollmentStatus(
            final String status, final LocalDateTime currentTime, final LocalDateTime endTime) {
        if ("COMPLETED".equals(status) && currentTime.isBefore(endTime)) {
            return EnrollmentStatus.ACTIVE;
        }
        return EnrollmentStatus.fromValue(status);
    }

    @SuppressWarnings("PMD.AvoidLiteralsInIfCondition")
    private MilestoneStatus calculateStatus(
            final String status,
            final LocalDateTime currentTime,
            final LocalDateTime startTime,
            final LocalDateTime endTime) {
        if (("ACTIVE".equals(status) || "SKIP".equals(status)) && currentTime.isBefore(startTime)) {
            return MilestoneStatus.LOCKED;
        }

        if ("COMPLETED".equals(status) && currentTime.isBefore(endTime)) {
            return MilestoneStatus.ACTIVE;
        }
        return MilestoneStatus.fromValue(status);
    }

    private List<UserMilestoneEnrollmentDto> getMilestones(
            final JourneyEnrollment journeyEnrollment, final LocalDateTime time) {
        if (journeyEnrollment == null || journeyEnrollment.getJourneyEnrollmentMilestones() == null) {
            return Collections.emptyList();
        }
        return journeyEnrollment.getJourneyEnrollmentMilestones().stream()
                .map(milestone -> getUserMilestoneEnrollmentDto(milestone, time))
                .sorted((o1, o2) -> (int) (o1.getIteration() - o2.getIteration()))
                .collect(Collectors.toList());
    }

    public UserMilestoneEnrollmentDto getUserMilestoneEnrollmentDto(
            final JourneyEnrollmentMilestone milestone, final LocalDateTime time) {
        if (milestone == null) {
            return null;
        }
        final MilestoneStatus milestoneStatus = calculateStatus(
                milestone.getMilestoneStatus(), time, milestone.getMilestoneFrom(), milestone.getMilestoneTo());
        return UserMilestoneEnrollmentDto.builder()
                .milestoneId(milestone.getJourneyEnrollmentMilestoneId())
                .iteration(milestone.getMilestoneIteration())
                .milestoneFrom(milestone.getMilestoneFrom())
                .milestoneTo(milestone.getMilestoneTo())
                .status(milestoneStatus)
                .activities(getJourneyActivities(milestone, milestoneStatus, time))
                .rewards(getMilestoneRewards(milestone, time))
                .build();
    }

    public CurrentUserMilestoneEnrollmentDto getCurrentUserMilestoneEnrollmentDto(
            final Optional<JourneyEnrollmentMilestone> milestone, final LocalDateTime time) {
        return milestone
                .map(journeyEnrollmentMilestone -> {
                    final UserMilestoneEnrollmentDto userMilestoneEnrollmentDto =
                            getUserMilestoneEnrollmentDto(journeyEnrollmentMilestone, time);

                    return new CurrentUserMilestoneEnrollmentDto(userMilestoneEnrollmentDto.toBuilder())
                            .toBuilder()
                                    .currentProgramId(journeyEnrollmentMilestone
                                            .getJourneyEnrollment()
                                            .getJourneyProgram()
                                            .getJourneyProgramId())
                                    .build();
                })
                .orElse(null);
    }

    private List<UserEnrollmentMilestoneReward> getMilestoneRewards(
            final JourneyEnrollmentMilestone milestone, final LocalDateTime time) {
        if ("COMPLETED".equals(milestone.getMilestoneStatus())) {
            return milestone.getJourneyEnrollmentMilestoneAwards().stream()
                    .map(award -> UserEnrollmentMilestoneReward.builder()
                            .rewardType(award.getRewardType())
                            .rewardValue(award.getRewardValue())
                            .awardedAt(award.getAwardDate())
                            .status(award.getAwardDate() == null ? "PENDING" : "AWARDED")
                            .build())
                    .collect(Collectors.toList());
        } else if ("ACTIVE".equals(milestone.getMilestoneStatus())) {
            return rewardFacade.getMilestoneReward(milestone, time).stream()
                    .map(reward -> UserEnrollmentMilestoneReward.builder()
                            .rewardType(reward.getRewardType())
                            .rewardValue(reward.getRewardValue())
                            .awardedAt(null)
                            .status("AVAILABLE")
                            .build())
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private List<UserMilestoneEnrollmentDto.ActivityDetails> getJourneyActivities(
            final JourneyEnrollmentMilestone milestone,
            final MilestoneStatus milestoneStatus,
            final LocalDateTime time) {
        if (milestone == null || milestone.getJourneyEnrollmentMilestoneActivities() == null) {
            return Collections.emptyList();
        }
        return milestone.getJourneyEnrollmentMilestoneActivities().stream()
                .map(activity -> getActivityDetails(activity, milestoneStatus, time))
                .collect(Collectors.toList());
    }

    private UserMilestoneEnrollmentDto.ActivityDetails getActivityDetails(
            final JourneyEnrollmentMilestoneActivity activity,
            final MilestoneStatus milestoneStatus,
            final LocalDateTime time) {
        if (activity == null) {
            return null;
        }
        return UserMilestoneEnrollmentDto.ActivityDetails.builder()
                .milestoneActivityId(activity.getJourneyEnrollmentMilestoneActivityId())
                .activityAmount(activity.getActivityAmount())
                .activityCompletionCount(activity.getActivityCompletionCount())
                .isMandatory(activity.getActivityFlexibility().equals(Flexibility.MANDATORY.getDbValue()))
                .mnemonic(activity.getActivityMnemonicId())
                .type(ActivityDetailsType.valueOf(activity.getActivityType()))
                .name(activity.getActivityName())
                .icon(activity.getActivityIcon())
                .status(calculateActivityStatus(activity.getActivityStatus(), milestoneStatus))
                .preconditionResults(activityCompletionValidator.evaluateActivityCompletion(activity, time))
                .build();
    }

    @SuppressWarnings("PMD.AvoidLiteralsInIfCondition")
    private String calculateActivityStatus(final String activityStatus, final MilestoneStatus milestoneStatus) {
        if (milestoneStatus == MilestoneStatus.ACTIVE) {
            return activityStatus;
        }
        if (milestoneStatus == MilestoneStatus.COMPLETED && "ACTIVE".equals(activityStatus)) {
            return "NOT_ACHIEVED";
        }
        if (milestoneStatus == MilestoneStatus.LOCKED && "ACTIVE".equals(activityStatus)) {
            return "LOCKED";
        }
        return activityStatus;
    }

    public JourneyBehaviourDto getJourneyBehaviourDto(final JourneyProgramBehaviour journeyProgramBehaviour) {
        if (journeyProgramBehaviour == null) {
            return null;
        }
        return JourneyBehaviourDto.builder()
                .milestoneType(journeyProgramBehaviour.getJourneyMilestone().getDescription())
                .programDuration(journeyProgramBehaviour.getProgramDuration())
                .build();
    }
}
