package za.co.discovery.health.journey.util;

import lombok.experimental.UtilityClass;

@UtilityClass
public class Constants {
    public static final String ROLE = "ROLE";
    public static final String ALLIANCE = "ALLIANCE";
    public static final String BRANCH = "BRANCH";
    public static final String GROUP = "GROUP";
    public static final String ENTITY_NO = "ENTITY_NO";
    public static final String ENROLLMENT_START_TIME = "ENROLLMENT_START_TIME";
    public static final String COACHING_JOURNEY_CATEGORY_TYPE_NAME = "COACHING";
    public static final String ENROL_IN_PERSONAL_JOURNEY = "ENROL_IN_PERSONAL_JOURNEY";
    public static final String MIDPOINT_ASSESSMENT_REMINDER = "MIDPOINT_ASSESSMENT_REMINDER";
    public static final String MIDPOINT_ASSESSMENT_MNEMONIC = "NSBM";
    public static final String MIDPOINT_ASSESSMENT_ACTIVITY_NAME = "Complete Mid Session Assessment";
}
