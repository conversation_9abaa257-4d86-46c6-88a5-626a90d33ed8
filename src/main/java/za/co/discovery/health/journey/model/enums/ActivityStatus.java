package za.co.discovery.health.journey.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ActivityStatus {
    ACTIVE("ACTIVE"),
    COMPLETED("COMPLETED"),
    LATE_COMPLETED("LATE_COMPLETED");

    private final String value;

    public static ActivityStatus fromValue(final String value) {
        for (final ActivityStatus status : values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown activity status: " + value);
    }
}
