package za.co.discovery.health.journey.service.journey;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import za.co.discovery.health.journey.config.exception.CoreException;
import za.co.discovery.health.journey.config.exception.ReasonCode;
import za.co.discovery.health.journey.database.databaseMapping.JourneyCategory;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestone;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestoneActivity;
import za.co.discovery.health.journey.database.databaseMapping.JourneyEnrollmentMilestoneActivityTxn;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentMilestoneActivityRepository;
import za.co.discovery.health.journey.database.repository.ExtendedJourneyEnrollmentMilestoneRepository;
import za.co.discovery.health.journey.event.ActivityCompletedEvent;
import za.co.discovery.health.journey.event.JourneyDrivenActivityCompletedEvent;
import za.co.discovery.health.journey.event.JourneyEventPublisher;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;
import za.co.discovery.health.journey.model.enums.ActivityStatus;
import za.co.discovery.health.journey.model.request.CompleteMilestoneActivity;
import za.co.discovery.health.journey.model.request.SearchMilestoneActivityRequest;
import za.co.discovery.health.journey.model.response.MilestoneActivityDto;
import za.co.discovery.health.journey.model.response.MilestoneActivityPreconditionResponse;
import za.co.discovery.health.journey.strategy.ActivityCompletionValidator;
import za.co.discovery.health.journey.util.TransactionUtils;

import javax.validation.Valid;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class JourneyActivityService {

    private final ExtendedJourneyEnrollmentMilestoneActivityRepository activityRepository;
    private final ExtendedJourneyEnrollmentMilestoneRepository milestoneRepository;
    private final ActivityCompletionValidator completionValidator;
    private final JourneyEventPublisher eventPublisher;

    @Transactional
    public void processActivityCompletion(
            final Long entityId,
            final String mnemonic,
            final Long activityTxnId,
            final LocalDateTime completedAt,
            final String value,
            final ActivityDetailsType activityType) {

        validateInputs(entityId, mnemonic, activityTxnId, completedAt);

        // Find all milestones containing the activity, not just active ones
        final List<JourneyEnrollmentMilestoneActivity> matchingActivities =
                findActivitiesByMnemonic(entityId, mnemonic, activityType);

        if (matchingActivities.isEmpty()) {
            log.warn("No activities found for entity {} with mnemonic {}", entityId, mnemonic);
            return;
        }

        matchingActivities.forEach(
                activity -> processActivityWithValidation(activity, activityTxnId, value, completedAt, entityId));
    }

    @Transactional
    public void processActivityCompletion(final CompleteMilestoneActivity request, boolean sendJourneyDrivenEvent) {
        final JourneyEnrollmentMilestoneActivity activity = activityRepository
                .findById(request.getMilestoneActivityId())
                .orElseThrow(() -> new CoreException(
                        ReasonCode.VALIDATION_ERROR, "Activity not found: " + request.getMilestoneActivityId()));

        if (!ActivityDetailsType.JOURNEY_DRIVEN_ACTIVITY.name().equals(activity.getActivityType())) {
            throw new CoreException(
                    ReasonCode.VALIDATION_ERROR,
                    "Activity is not an JOURNEY_DRIVEN_ACTIVITY type: " + activity.getActivityMnemonicId());
        }

        processActivityWithValidation(
                activity,
                activity.getJourneyEnrollmentMilestoneActivityId(),
                request.getValue(),
                request.getCompletedAt(),
                activity.getJourneyEnrollmentMilestone().getJourneyEnrollment().getEntityId());

        if (sendJourneyDrivenEvent) {
            TransactionUtils.afterCommit(() ->
                eventPublisher.publish(new JourneyDrivenActivityCompletedEvent(activity, request.getCompletedAt())));
        }
    }

    @Transactional
    public void processActivityWithValidation(
            final JourneyEnrollmentMilestoneActivity activity,
            final Long activityTxnId,
            final String value,
            final LocalDateTime completedAt,
            final Long entityId) {

        if (isDuplicateTransaction(activity, activityTxnId)) {
            log.debug(
                    "Duplicate activity transaction {} for activity {}",
                    activityTxnId,
                    activity.getActivityMnemonicId());
            return;
        }

        // Validate preconditions before processing
        if (!completionValidator.canActivityBeCompleted(activity, completedAt)) {
            log.warn(
                    "Activity {} cannot be completed for entity {} - preconditions not met",
                    activity.getActivityMnemonicId(),
                    entityId);
            return;
        }

        // Determine if this is a late completion
        final boolean isLateCompletion = isLateCompletion(activity, completedAt);

        // Process the activity
        processActivityProgress(activity, activityTxnId, value, completedAt, isLateCompletion);
    }

    @Transactional(readOnly = true)
    public Optional<JourneyEnrollmentMilestoneActivity> getByMnemonicAndIteration(
            final Long entityId, final Long programId, final String mnemonic, final Long iteration) {
        return activityRepository.getActivityByIteration(programId, entityId, iteration, mnemonic);
    }

    private void validateInputs(
            final Long entityId, final String mnemonic, final Long activityTxnId, final LocalDateTime completedAt) {
        Assert.notNull(entityId, "Entity ID cannot be null");
        Assert.hasText(mnemonic, "Activity mnemonic cannot be null or empty");
        Assert.notNull(activityTxnId, "Activity transaction ID cannot be null");
        Assert.notNull(completedAt, "Completion time cannot be null");
    }

    private List<JourneyEnrollmentMilestoneActivity> findActivitiesByMnemonic(
            final Long entityId, final String mnemonic, final ActivityDetailsType activityType) {

        final List<JourneyEnrollmentMilestone> allMilestones =
                milestoneRepository.findAllMilestonesByEntityIdWithActivities(entityId);

        return allMilestones.stream()
                .flatMap(milestone -> milestone.getJourneyEnrollmentMilestoneActivities().stream())
                .filter(activity -> mnemonic.equals(activity.getActivityMnemonicId())
                        && activityType.name().equals(activity.getActivityType()))
                .collect(Collectors.toList());
    }

    /**
     * Determines if the activity completion is late based on milestone timing
     */
    private boolean isLateCompletion(
            final JourneyEnrollmentMilestoneActivity activity, final LocalDateTime completedAt) {

        final JourneyEnrollmentMilestone milestone = activity.getJourneyEnrollmentMilestone();
        return !isMilestoneActiveAtTime(milestone, completedAt);
    }

    /**
     * Checks if a milestone was active at the given time
     */
    private boolean isMilestoneActiveAtTime(
            final JourneyEnrollmentMilestone milestone, final LocalDateTime completedAt) {

        final LocalDateTime milestoneStart = milestone.getMilestoneFrom();
        final LocalDateTime milestoneEnd = milestone.getMilestoneTo();

        return !completedAt.isBefore(milestoneStart) && (milestoneEnd == null || !completedAt.isAfter(milestoneEnd));
    }

    private boolean isDuplicateTransaction(
            final JourneyEnrollmentMilestoneActivity activity, final Long activityTxnId) {
        return activity.getJourneyEnrollmentMilestoneActivityTxns().stream()
                .anyMatch(txn -> Objects.equals(txn.getActivityTransactionId(), activityTxnId));
    }

    private void processActivityProgress(
            final JourneyEnrollmentMilestoneActivity activity,
            final Long activityTxnId,
            final String value,
            final LocalDateTime completedAt,
            final boolean isLateCompletion) {

        if (isActivityAlreadyCompleted(activity)) {
            log.debug("Activity {} is already completed", activity.getActivityMnemonicId());
            return;
        }

        incrementActivityProgress(activity);
        final boolean wasJustCompleted = checkAndMarkActivityAsCompleted(activity, isLateCompletion, completedAt);
        addActivityTransaction(activity, activityTxnId, value, completedAt);

        activityRepository.save(activity);

        log.info(
                "Processed activity {} for entity {}. Progress: {}/{}, Late: {}",
                activity.getActivityMnemonicId(),
                activity.getJourneyEnrollmentMilestone().getJourneyEnrollment().getEntityId(),
                activity.getActivityCompletionCount(),
                activity.getActivityAmount(),
                isLateCompletion);

        // Publish event if activity was just completed
        if (wasJustCompleted) {
            publishActivityCompletedEvent(activity, completedAt, isLateCompletion);
        }
    }

    private boolean isActivityAlreadyCompleted(final JourneyEnrollmentMilestoneActivity activity) {
        final String status = activity.getActivityStatus();
        return ActivityStatus.COMPLETED.getValue().equals(status)
                || ActivityStatus.LATE_COMPLETED.getValue().equals(status);
    }

    private void incrementActivityProgress(final JourneyEnrollmentMilestoneActivity activity) {
        final long newCount = activity.getActivityCompletionCount() + 1;
        activity.setActivityCompletionCount(newCount);
    }

    /**
     * Marks activity as completed with appropriate status and returns true if it was just completed
     */
    private boolean checkAndMarkActivityAsCompleted(
            final JourneyEnrollmentMilestoneActivity activity,
            final boolean isLateCompletion,
            final LocalDateTime completedAt) {

        if (activity.getActivityCompletionCount() >= activity.getActivityAmount()) {
            final String statusToSet =
                    isLateCompletion ? ActivityStatus.LATE_COMPLETED.getValue() : ActivityStatus.COMPLETED.getValue();

            activity.setActivityStatus(statusToSet);
            activity.setCompletedAt(completedAt);

            log.info(
                    "Activity {} {} for entity {}",
                    activity.getActivityMnemonicId(),
                    isLateCompletion ? "completed late" : "completed",
                    activity.getJourneyEnrollmentMilestone()
                            .getJourneyEnrollment()
                            .getEntityId());

            return true;
        }

        return false;
    }

    private void addActivityTransaction(
            final JourneyEnrollmentMilestoneActivity activity,
            final Long activityTxnId,
            final String value,
            final LocalDateTime completedAt) {

        final JourneyEnrollmentMilestoneActivityTxn transaction =
                new JourneyEnrollmentMilestoneActivityTxn(activity, completedAt, activityTxnId, value);

        activity.getJourneyEnrollmentMilestoneActivityTxns().add(transaction);
    }

    private void publishActivityCompletedEvent(
            final JourneyEnrollmentMilestoneActivity activity,
            final LocalDateTime completedAt,
            final boolean isLateCompletion) {

        final ActivityCompletedEvent event =
                new ActivityCompletedEvent(activity, ActivityStatus.valueOf(activity.getActivityStatus()), completedAt);

        eventPublisher.publish(event);

        log.debug(
                "Published ActivityCompletedEvent for activity {} (late: {})",
                activity.getActivityMnemonicId(),
                isLateCompletion);
    }

    @Transactional(readOnly = true)
    public MilestoneActivityDto getMilestoneActivity(final Long milestoneActivityId) {
        return activityRepository
                .findById(milestoneActivityId)
                .map(milestoneActivity -> {
                    final String value = milestoneActivity.getJourneyEnrollmentMilestoneActivityTxns().stream()
                            .findFirst()
                            .map(JourneyEnrollmentMilestoneActivityTxn::getValue)
                            .orElse(null);

                    return MilestoneActivityDto.builder()
                            .activityMnemonicId(milestoneActivity.getActivityMnemonicId())
                            .journeyCategoryName(milestoneActivity
                                    .getJourneyEnrollmentMilestone()
                                    .getJourneyEnrollment()
                                    .getJourneyCategory()
                                    .getName())
                            .activityType(ActivityDetailsType.valueOf(milestoneActivity.getActivityType()))
                            .activityStatus(ActivityStatus.valueOf(milestoneActivity.getActivityStatus()))
                            .milestoneActivityId(milestoneActivity.getJourneyEnrollmentMilestoneActivityId())
                            .completedAt(milestoneActivity.getCompletedAt())
                            .activityValue(value)
                            .build();
                })
                .orElseThrow(() ->
                        new CoreException(ReasonCode.VALIDATION_ERROR, "Activity not found: " + milestoneActivityId));
    }

    public List<MilestoneActivityPreconditionResponse> getMilestoneActivityPreconditions(
            final @Valid SearchMilestoneActivityRequest request) {

        final List<JourneyEnrollmentMilestoneActivity> allActivitiesByMnemonic =
                activityRepository.findAllActivitiesByMnemonic(
                        request.getEntityId(),
                        request.getActivityMnemonicId(),
                        request.getStatus().getValue());
        return allActivitiesByMnemonic.stream()
                .map(activity -> {
                    final JourneyCategory journeyCategory = activity.getJourneyEnrollmentMilestone()
                            .getJourneyEnrollment()
                            .getJourneyCategory();
                    return MilestoneActivityPreconditionResponse.builder()
                            .journeyCategoryId(journeyCategory.getJourneyCategoryId())
                            .journeyCategoryName(journeyCategory.getName())
                            .preconditionEvaluationResultList(
                                    completionValidator.evaluateActivityCompletion(activity, request.getCurrentTime()))
                            .build();
                })
                .collect(Collectors.toList());
    }
}
