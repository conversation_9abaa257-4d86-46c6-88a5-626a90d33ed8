package za.co.discovery.health.journey.util;

import lombok.experimental.UtilityClass;

@UtilityClass
public class DMNConstants {
    public static final String TIME_PERIOD_UNIT = "timePeriodUnit";
    public static final String TIME_PERIOD_VALUE = "timePeriodValue";
    public static final String COMPLETE_TIMES = "completeTimes";
    public static final String MILESTONE_VALUE = "milestoneValue";
    public static final String ALLIANCE_NO = "alliance";
    public static final String BRANCH_NO = "branch";
    public static final String GROUP_NO = "group";
    public static final String ITERATION = "iteration";
    public static final String MILESTONE_TRANSITION_ACTIVITIES = "activities";
    public static final String PROGRAM_TRANSITION = "programDetails";

    public static final String ACTIVITY_ID = "ActivityId";
    public static final String COMPLETION_ACTIVITY_ID = "activityId";
    public static final String FREQUENCY = "frequency";
    public static final String COMPLETION_TYPE = "completionType";

    public static final String ACTIVITY_NAME = "activityName";
    public static final String ACTIVITY_ICON = "activityIcon";
    public static final String ACTIVITY_TYPE = "activityType";
    public static final String IS_SKIP_MILESTONE = "isSkipMilestone";

    public static final String JOURNEY_CATEGORY = "journeyCategory";
    public static final String JOURNEY_ENROLLMENT_START_TIME = "enrollmentStartTime";
    public static final String JOURNEY_ENROLLMENT_END_TIME = "enrollmentEndTime";
    public static final String JOURNEY_START_TIME = "journeyStartTime";
    public static final String JOURNEY_ACTIVITY_PRECONDITION = "activityPrecondition";
    public static final String JOURNEY_ENROLLMENT_PRECONDITION = "enrollmentPrecondition";

    public static final String ACTIVITY_TYPE_CATEGORY = "category";
    public static final String PROGRAM = "program";
    public static final String ACTIVITY_COUNT = "activityCount";
    public static final String JOURNEY_MAX_PARTICIPANTS = "maxParticipants";
    public static final String JOURNEY_MONITORING_PERIOD_DURATION = "monitoringPeriodDuration";

    public static final String COMPLETION_PRECONDITION_TYPE = "completionPreconditionType";
    public static final String COMPLETION_IDENTIFIER = "completionIdentifier";
}
