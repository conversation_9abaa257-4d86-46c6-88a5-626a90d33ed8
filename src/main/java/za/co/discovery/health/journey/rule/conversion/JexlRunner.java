package za.co.discovery.health.journey.rule.conversion;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.jexl3.JexlBuilder;
import org.apache.commons.jexl3.JexlContext;
import org.apache.commons.jexl3.JexlEngine;
import org.apache.commons.jexl3.JexlScript;
import org.apache.commons.jexl3.MapContext;
import org.apache.commons.jexl3.introspection.JexlPermissions;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import za.co.discovery.health.journey.remote.pacman.service.PacManService;
import za.co.discovery.health.journey.resolver.rule.model.request.MilestoneTransitionRuleRequest;
import za.co.discovery.health.journey.resolver.rule.model.request.ProgramTransitionRuleRequest;
import za.co.discovery.health.journey.rule.conversion.operators.ActivityCompletedMethod;
import za.co.discovery.health.journey.rule.conversion.operators.MilestoneTransitionMethod;
import za.co.discovery.health.journey.rule.conversion.operators.ProgramTransitionMethod;
import za.co.discovery.health.journey.util.Constants;
import za.co.discovery.health.journey.util.DMNConstants;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
@AllArgsConstructor
public class JexlRunner {

    private final PacManService pacManService;

    final JexlEngine jexl = new JexlBuilder()
            .silent(false)
            .cache(1024)
            .strict(true)
            .debug(true)
            .permissions(JexlPermissions.UNRESTRICTED)
            .create();

    @SuppressWarnings("unchecked")
    public boolean run(final String expression, final Map<String, Object> count) {
        final StopWatch stopWatch = new StopWatch("Run Conversion");
        stopWatch.start("Conversion");
        final JexlContext context = new MapContext();
        count.forEach(context::set);
        context.set("milestone", new MilestoneTransitionMethod((List<
                        MilestoneTransitionRuleRequest.MilestoneActivityDetails>)
                context.get(DMNConstants.MILESTONE_TRANSITION_ACTIVITIES)));
        context.set("program", new ProgramTransitionMethod((ProgramTransitionRuleRequest)
                context.get(DMNConstants.PROGRAM_TRANSITION)));
        context.set(
                "activity",
                new ActivityCompletedMethod(
                        pacManService, Long.parseLong((String) context.get(Constants.ENTITY_NO)), (LocalDateTime)
                                context.get(Constants.ENROLLMENT_START_TIME)));

        final JexlScript script = jexl.createScript("result=" + expression);

        script.execute(context);

        stopWatch.stop();
        log.info("completed in {} ms", stopWatch.getTotalTimeMillis());
        log.info(stopWatch.prettyPrint());
        return (Boolean) context.get("result");
    }

    public List<String> extractVariables(final String expression) {

        final JexlScript script = jexl.createScript(expression);

        final Set<List<String>> variables = script.getVariables();
        if (variables == null) {
            return List.of();
        }

        return variables.stream().flatMap(Collection::stream).collect(Collectors.toList());
    }
}
