package za.co.discovery.health.journey.model.enums;

public enum ActivityDetailsType {
    JOURNEY_DRIVEN_ACTIVITY,
    ACTIVITY,
    APPOINTMENT,
    SKIP;

    public static ActivityDetailsType fromValue(final String type) {
        for (final ActivityDetailsType activityDetailsType : ActivityDetailsType.values()) {
            if (activityDetailsType.name().equalsIgnoreCase(type)) {
                return activityDetailsType;
            }
        }
        throw new IllegalArgumentException("Unknown type: " + type);
    }
}
