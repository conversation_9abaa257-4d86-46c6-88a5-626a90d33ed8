package za.co.discovery.health.journey.facade;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import za.co.discovery.health.journey.messaging.model.ActivityAllocationStatusEnum;
import za.co.discovery.health.journey.messaging.model.ActivityCompletedEvent;
import za.co.discovery.health.journey.messaging.model.PatientAppointmentEvent;
import za.co.discovery.health.journey.model.enums.ActivityDetailsType;
import za.co.discovery.health.journey.model.request.CompleteMilestoneActivity;
import za.co.discovery.health.journey.model.request.SearchMilestoneActivityRequest;
import za.co.discovery.health.journey.model.response.MilestoneActivityDto;
import za.co.discovery.health.journey.model.response.MilestoneActivityPreconditionResponse;
import za.co.discovery.health.journey.remote.pacman.service.PacManService;
import za.co.discovery.health.journey.remote.vap.VapService;
import za.co.discovery.health.journey.service.journey.JourneyActivityService;
import za.co.discovery.health.pacman.domain.ActivityTransactionResponse;

import javax.validation.Valid;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class ActivityCompletionFacade {

    private final JourneyActivityService activityService;

    private final PacManService pacManService;

    private final VapService vapService;

    private static final String SCALE_WEIGHT_IN = "SSWI";

    private static final String MANUAL_WEIGHT_IN = "MNWI";

    public void completeActivity(final ActivityCompletedEvent event) {
        if (ActivityAllocationStatusEnum.ALLOCATED.equals(event.getAllocationStatusEnum())
                || event.getAllocationStatusEnum() == null) {
            pacManService
                    .getTransaction(Long.valueOf(event.getEntityId()), List.of(event.getActivityTransactionId()))
                    .filter(it -> it.containsKey(event.getActivityTransactionId())
                            && it.get(event.getActivityTransactionId()) != null
                            && it.get(event.getActivityTransactionId()).getEventId() != null
                            && Objects.equals(
                                    it.get(event.getActivityTransactionId()).getActivityAllocationStatus(),
                                    ActivityAllocationStatusEnum.ALLOCATED.getApiStatus()))
                    .subscribe(activityTxn -> {
                        final var activityTransaction = activityTxn.get(event.getActivityTransactionId());

                        if (Objects.equals(activityTransaction.getEventId(), SCALE_WEIGHT_IN)) {
                            processScaleWeightIn(event, activityTransaction);
                        }
                        if (Objects.equals(activityTransaction.getEventId(), MANUAL_WEIGHT_IN)) {
                            processManualWeightIn(event, activityTransaction);
                        } else {
                            processActivity(event, activityTransaction);
                        }
                    });
        }
    }

    private void processScaleWeightIn(
            final ActivityCompletedEvent event, final ActivityTransactionResponse activityTransaction) {
        vapService
                .getActivityValue(event.getEnrolmentId(), event.getTransactionDate())
                .ifPresent(activityValue -> activityService.processActivityCompletion(
                        Long.valueOf(event.getEntityId()),
                        MANUAL_WEIGHT_IN,
                        activityTransaction.getId(),
                        Objects.requireNonNull(activityTransaction.getTransactionDate())
                                .toLocalDateTime(),
                        String.valueOf(activityValue.getValue()),
                        ActivityDetailsType.JOURNEY_DRIVEN_ACTIVITY));
    }

    private void processManualWeightIn(
            final ActivityCompletedEvent event, final ActivityTransactionResponse activityTransaction) {
        activityService.processActivityCompletion(
                Long.valueOf(event.getEntityId()),
                activityTransaction.getEventId(),
                activityTransaction.getId(),
                Objects.requireNonNull(activityTransaction.getTransactionDate()).toLocalDateTime(),
                activityTransaction.getActivityValue(),
                ActivityDetailsType.JOURNEY_DRIVEN_ACTIVITY);
    }

    private void processActivity(
            final ActivityCompletedEvent event, final ActivityTransactionResponse activityTransaction) {
        activityService.processActivityCompletion(
                Long.valueOf(event.getEntityId()),
                activityTransaction.getEventId(),
                activityTransaction.getId(),
                Objects.requireNonNull(activityTransaction.getCreatedDate()).toLocalDateTime(),
                event.getValue(),
                ActivityDetailsType.ACTIVITY);
    }

    public void completeAppointment(final PatientAppointmentEvent event) {
        if ("PATIENT".equals(event.getRole())) {
            activityService.processActivityCompletion(
                    event.getEntityNo(),
                    String.valueOf(event.getAppointmentId()),
                    event.getAppointmentId(),
                    LocalDateTime.now(),
                    null,
                    ActivityDetailsType.APPOINTMENT);
        }
    }

    public void completeMilestoneActivity(final CompleteMilestoneActivity event) {
        activityService.processActivityCompletion(event, true);
    }

    public MilestoneActivityDto getMilestoneActivity(final Long milestoneActivityId) {
        return activityService.getMilestoneActivity(milestoneActivityId);
    }

    public List<MilestoneActivityPreconditionResponse> getMilestoneActivityPreconditions(
            final @Valid SearchMilestoneActivityRequest request) {
        return activityService.getMilestoneActivityPreconditions(request);
    }
}
