spring:
  application:
    name: hs-personal-journey
  profiles.include: postgres-db, b2b, kafka, shedlock-jdbc
  autoconfigure:
    # This overrides our security configuration
    exclude: org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration
  main:
    allow-bean-definition-overriding: true
  cloud:
    config:
      enabled: false
  flyway:
    enabled: true
    locations: classpath:database/db/migration
  jpa:
    properties:
      hibernate:
        show_sql: true
        id:
          new_generator_mappings: true
        current_session_context_class: thread
        temp:
          use_jdbc_metadata_defaults: false
    hibernate:
      ddl-auto: validate
  kafka:
    consumer:
      auto-offset-reset: earliest
      group-id: hs-personal-journey
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: "*"
      enable-auto-commit: true
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
    listener:
      ack-mode: manual
      client-id: pathfinder-client-id
      concurrency: 1
      type: batch
    admin:
      client-id: admin-pathfinder-client-id
    bootstrap-servers: localhost:9092

platform:
  logging:
    console-level: "info"
    json-console-level: "off"
    flat-file-level: "off"
    json-file-level: "off"
  oauth2:
    resourceserver:
      cors:
        allowed-origin-patterns: '*'
        allowed-methods: GET,HEAD,POST,PUT,PATCH,DELETE,OPTIONS,TRACE
        allowed-headers: X-XSRF-TOKEN,XSRF-TOKEN,CONTENT-TYPE
        allow-credentials: true
        max-age: 10

scheduler:
  mid-point-reminder-cron: "0 0 6 * * *"

#logging:
#  level:
#    org.hibernate.SQL: DEBUG
#    org.hibernate.type: TRACE

wiremock:
  port: ${wiremock.server.port}


batch:
  page:
    size: 1000
---
spring:
  profiles:
    active: test-service-urls

integration:
  pac-man:
    url: http://localhost:${wiremock.port}
  config-flag:
    url: http://localhost:${wiremock.port}
  vap:
    url: http://localhost:${wiremock.port}
  group-coaching:
    url: http://localhost:${wiremock.port}

hs:
  oauth2:
    url: https://integrationtest.powerofvitality.com/v3-auth/realms/vhs/protocol/openid-connect/token
    client:
      id: backend-to-backend-client
      secret: cjL9jRlVMh8edhWdbe090VBqZ9LJlSAT

security:
  oauth2:
    client:
      client-id: hs-platform
      client-secret: uAcbVei2Nnj6tmQh2g7B4LOaqZwuRj7v
      scope: openid,profile,email,roles
      oauth-base-url: http://localhost:8080/v3-auth/realms/vhs/protocol/openid-connect/auth
    resource:
      id: hs-platform
      token-info-uri: http://localhost:8080/v3-auth/realms/vhs/protocol/openid-connect/token
      user-info-uri: http://localhost:8080/v3-auth/realms/vhs/protocol/openid-connect/userinfo
      jwk:
        key-set-uri: http://localhost:8080/v3-auth/realms/vhs/protocol/openid-connect/certs

