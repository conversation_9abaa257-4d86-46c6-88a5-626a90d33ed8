## src/test/resources/features/program_progression.feature
#
#Feature: Program Progression and Milestone Management
#  As a user progressing through a health journey
#  I want to automatically advance through milestones and programs
#  So that I can continue my health journey at the appropriate level
#
#  Background:
#    Given the rest application has started up
#    And set up authorized entity
#    And clean up events
#
#  Scenario: Milestone completion triggers next milestone creation
#    Given user 12345 has an active enrollment in "Weight Loss - Beginner"
#    And user 12345 has milestone 1 with status "ACTIVE" ending at "2024-06-15T23:59:59"
#    And milestone 1 has the following completed activities:
#      | activityMnemonic | completionCount | requiredAmount | status    |
#      | STEPS_5000       | 7               | 7              | COMPLETED |
#      | LOG_MEALS        | 7               | 7              | COMPLETED |
#      | WEIGH_IN         | 2               | 2              | COMPLETED |
#    When the system processes milestone completion at "2024-06-16T10:00:00"
#    Then milestone 1 should be marked as "COMPLETED"
#    And milestone 1 should have completion date "2024-06-16T10:00:00"
#    And milestone 2 should be created with status "ACTIVE"
#    And milestone 2 should have start date "2024-06-15T23:59:59"
#    And milestone 2 should have end date "2024-06-22T23:59:59"
#    And milestone rewards should be assigned for milestone 1
#
#  Scenario: Milestone expires without completion
#    Given user 12345 has milestone 1 with status "ACTIVE" ending at "2024-06-15T23:59:59"
#    And milestone 1 has the following incomplete activities:
#      | activityMnemonic | completionCount | requiredAmount | status |
#      | STEPS_5000       | 5               | 7              | ACTIVE |
#      | LOG_MEALS        | 7               | 7              | COMPLETED |
#    When the system processes milestone expiration at "2024-06-16T10:00:00"
#    Then milestone 1 should be marked as "NOT_ACHIEVED"
#    And milestone 2 should be created with status "ACTIVE"
#    And no milestone rewards should be assigned
#
#  Scenario: Program completion triggers transition to next program
#    Given user 12345 is enrolled in "Weight Loss - Beginner" program
#    And the program has completed 8 milestones as required
#    And the following programs are available in order:
#      | programId | name                   | order |
#      | 1         | Weight Loss - Beginner | 1     |
#      | 2         | Weight Loss - Advanced | 2     |
#    When the system evaluates program completion
#    Then the current enrollment should be marked as "COMPLETED"
#    And a new enrollment should be created for "Weight Loss - Advanced"
#    And the first milestone should be created for the new program
#    And program completion rewards should be assigned
#
#  Scenario: Journey completion when no more programs available
#    Given user 12345 is enrolled in "Weight Loss - Advanced" program
#    And this is the final program in the category
#    And the program completion criteria are met
#    When the system evaluates program completion
#    Then the current enrollment should be marked as "COMPLETED"
#    And no new enrollment should be created
#    And final journey completion rewards should be assigned
#
#  Scenario: Program progression blocked by incomplete milestones
#    Given user 12345 is enrolled in "Weight Loss - Beginner" program for 8 weeks
#    And the program has the following milestone completion:
#      | iteration | status        |
#      | 1         | COMPLETED     |
#      | 2         | COMPLETED     |
#      | 3         | NOT_ACHIEVED  |
#      | 4         | COMPLETED     |
#      | 5         | ACTIVE        |
#    When the system evaluates program completion
#    Then the enrollment should remain "ACTIVE"
#    And no program transition should occur
#    And the user should continue with current program
#
#  Scenario: Multiple milestone iterations in same time period
#    Given user 12345 has an active enrollment with milestone unit "DAYS"
#    And milestone 1 was completed on "2024-06-15"
#    When milestone 2 is created
#    Then milestone 2 should start on "2024-06-15T23:59:59"
#    And milestone 2 should end on "2024-06-16T23:59:59"
#    And milestone 2 should have iteration number 2
#
#  Scenario: Milestone creation with custom activities based on iteration
#    Given user 12345 has completed milestone 1 in "Weight Loss - Beginner"
#    And the activity recommendation rules specify different activities for iteration 2
#    When milestone 2 is created
#    Then milestone 2 should have the following activities:
#      | activityMnemonic | activityName           | requiredAmount |
#      | STEPS_7500       | Walk 7500 steps        | 7              |
#      | LOG_MEALS        | Log daily meals        | 7              |
#      | STRENGTH_TRAIN   | Strength training      | 3              |
#
#  Scenario: Concurrent milestone processing for multiple users
#    Given multiple users have milestones completing at the same time:
#      | userId | milestoneId | completionTime      |
#      | 12345  | 101         | 2024-06-15T10:00:00 |
#      | 67890  | 201         | 2024-06-15T10:00:00 |
#      | 11111  | 301         | 2024-06-15T10:00:00 |
#    When the system processes milestone completions concurrently
#    Then each user should have their next milestone created correctly
#    And no data corruption should occur
#    And all reward assignments should be processed
#
#  Scenario: Program transition with enrollment linking
#    Given user 12345 completes "Weight Loss - Beginner"
#    When a new enrollment is created for "Weight Loss - Advanced"
#    Then the old enrollment should reference the new enrollment
#    And the new enrollment should be marked as "ACTIVE"
#    And the transition date should be recorded
#
#  Scenario: Retrieve milestone completion history
#    Given user 12345 has the following milestone history:
#      | iteration | status        | completedDate | milestoneFrom | milestoneTo |
#      | 1         | COMPLETED     | 2024-06-08    | 2024-06-01    | 2024-06-08  |
#      | 2         | COMPLETED     | 2024-06-15    | 2024-06-08    | 2024-06-15  |
#      | 3         | NOT_ACHIEVED  | null          | 2024-06-15    | 2024-06-22  |
#      | 4         | ACTIVE        | null          | 2024-06-22    | 2024-06-29  |
#    When user 12345 requests their milestone history
#    Then the response should include all 4 milestones
#    And completed milestones should show completion dates
#    And active milestones should show current progress