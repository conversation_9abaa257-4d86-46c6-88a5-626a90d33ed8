#Feature: journey integration test
#
#  Background:
#    Given the rest application has started up
#    And dummy file is imported
#
##  Scenario:
##    Given i get user enrollments
#
#  Scenario: User enrollments
#    When i call user enrollments
#    Then i should get user enrollments of size 2
#
#    And i should get user enrollments with category FITNESS MANAGEMENT
#    And i should get user enrollments with program size 1 in category FITNESS MANAGEMENT
#    And i should get user enrollments with program Fitness Management in category FITNESS MANAGEMENT with status ACTIVE and order 1
#    And i should get user enrollments with milestone size 1 in program Fitness Management in category FITNESS MANAGEMENT
#    And i should get user enrollments with milestone iterations 1 in program Fitness Management in category FITNESS MANAGEMENT should be of status ACTIVE
#    And i should get user enrollments with milestone iteration 1 with activity size 4 in program WM Get To Know in category Weight Management
#    And i should get user enrollments with milestone iteration 1 with activity BQUI1 in program Fitness Management in category FITNESS MANAGEMENT should be of status ACTIVE and completion count 0 and amount 1 and isMandatory false
#    And i should get user enrollments with milestone iteration 1 with activity BVID1 in program Fitness Management in category FITNESS MANAGEMENT should be of status ACTIVE and completion count 0 and amount 1 and isMandatory false
#    And i should get user enrollments with milestone iteration 1 with activity GSTR in program Fitness Management in category FITNESS MANAGEMENT should be of status ACTIVE and completion count 0 and amount 1 and isMandatory true
#    And i should get user enrollments with milestone iteration 1 with activity GEXR in program Fitness Management in category FITNESS MANAGEMENT should be of status ACTIVE and completion count 0 and amount 2 and isMandatory true
#
#    And i should get user enrollments with category Weight Management
#    And i should get user enrollments with program size 1 in category Weight Management
#    And i should get user enrollments with program WM Get To Know in category Weight Management with status ACTIVE and order 1
#    And i should get user enrollments with milestone size 1 in program WM Get To Know in category Weight Management
#    And i should get user enrollments with milestone iterations 1 in program WM Get To Know in category Weight Management should be of status ACTIVE
#    And i should get user enrollments with milestone iteration 1 with activity size 4 in program Fitness Management in category FITNESS MANAGEMENT
#    And i should get user enrollments with milestone iteration 1 with activity GSTR in program WM Get To Know in category Weight Management should be of status ACTIVE and completion count 0 and amount 1 and isMandatory true
#    And i should get user enrollments with milestone iteration 1 with activity BVID1 in program WM Get To Know in category Weight Management should be of status ACTIVE and completion count 0 and amount 1 and isMandatory false
#    And i should get user enrollments with milestone iteration 1 with activity GEXR in program WM Get To Know in category Weight Management should be of status ACTIVE and completion count 0 and amount 2 and isMandatory true
#    And i should get user enrollments with milestone iteration 1 with activity BQUI1 in program WM Get To Know in category Weight Management should be of status ACTIVE and completion count 0 and amount 1 and isMandatory false
#
#    Then i complete an activity with mnemonic GSTR with txn 1 with date now
#    When i call user enrollments
#    And i should get user enrollments with milestone iteration 1 with activity GSTR in program WM Get To Know in category Weight Management should be of status COMPLETED and completion count 1 and amount 1 and isMandatory true
#    And i should get user enrollments with milestone iteration 1 with activity GSTR in program Fitness Management in category FITNESS MANAGEMENT should be of status ACTIVE and completion count 1 and amount 1 and isMandatory true
#
