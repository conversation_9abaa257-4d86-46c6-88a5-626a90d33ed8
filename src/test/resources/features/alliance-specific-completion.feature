## ==========================================
## File: src/test/resources/features/alliance-specific-completion.feature
## ==========================================
#
#Feature: Alliance-Specific Journey Completion Rules
#  As a system administrator
#  I want different alliances to have different completion requirements
#  So that alliance-specific training standards are enforced
#
#  Background:
#    Given the rest application has started up
#    And the following users are enrolled in journey "Cross Alliance Collaboration":
#      | username | alliance   |
#      | alice    | Alliance A |
#      | bob      | Alliance B |
#    And the journey has started
#
#  @AllianceCompletion
#  Scenario: Alliance A completion with hasCompletedAllMandatoryActivitiesAtLeastOnce rule
#    Given alliance "Alliance A" has milestone transition rule "hasCompletedAllMandatoryActivitiesAtLeastOnce"
#    And the current milestone has activities:
#      | activity_name        | flexibility | required_count |
#      | Team Leadership      | MANDATORY   | 1              |
#      | Communication Setup  | MANDATORY   | 1              |
#      | Planning Session     | OPTIONAL    | 1              |
#    When user "alice" completes activities:
#      | activity_name        | completion_count |
#      | Team Leadership      | 1                |
#      | Communication Setup  | 1                |
#    Then user "alice" milestone should be marked as "COMPLETED"
#    And user "alice" should progress to the next milestone
#
#  @AllianceCompletion
#  Scenario: Alliance B completion with hasMetAllConditions rule
#    Given alliance "Alliance B" has milestone transition rule "hasMetAllConditions"
#    And the current milestone has activities:
#      | activity_name        | flexibility | required_count |
#      | Team Leadership      | MANDATORY   | 1              |
#      | Communication Setup  | MANDATORY   | 1              |
#      | Progress Review      | MANDATORY   | 1              |
#    When user "bob" completes activities:
#      | activity_name        | completion_count |
#      | Team Leadership      | 1                |
#      | Communication Setup  | 1                |
#    Then user "bob" milestone should be marked as "IN_PROGRESS"
#    And user "bob" should not progress to the next milestone
#    When user "bob" completes activities:
#      | activity_name        | completion_count |
#      | Progress Review      | 1                |
#    Then user "bob" milestone should be marked as "COMPLETED"
#    And user "bob" should progress to the next milestone
