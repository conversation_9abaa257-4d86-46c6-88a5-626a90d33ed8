#Feature: journey integration test V2
#
#  Background:
#    Given the rest application has started up
#    And dummy file is imported
#    And setup entity response with alliance 1 and group 1, branch string
#
#  Scenario: User enrollments V2
#    When i call user enrollments
#      | alliance | group  | branch | role   | time |
#      | string   | string | string | string | now  |
#
#    Then i should get user enrollments of size:
#      | count |
#      | 2     |
#
#    And i should get user enrollments with category:
#      | category           |
#      | FITNESS MANAGEMENT |
#      | Weight Management  |
#
#    And i should get user enrollments with program size:
#      | category           | programSize |
#      | FITNESS MANAGEMENT | 1           |
#      | Weight Management  | 1           |
#
#    Then i should get user enrollments with programs:
#      | category           | program            | status | order |
#      | FITNESS MANAGEMENT | Fitness Management | ACTIVE | 1     |
#      | Weight Management  | WM Get To Know     | ACTIVE | 1     |
#
#    Then i should get user enrollments with programs rewards size:
#      | category           | program            | rewardSize |
#      | FITNESS MANAGEMENT | Fitness Management | 1          |
#      | Weight Management  | WM Get To Know     | 1          |
#
#    Then i should get user enrollments with programs rewards:
#      | category           | program            | rewardValue | rewardType              | awardDate | status    |
#      | FITNESS MANAGEMENT | Fitness Management | 2           | LIFETIME GYM MEMBERSHIP | empty     | AVAILABLE |
#      | Weight Management  | WM Get To Know     | 1000        | POINT                   | empty     | AVAILABLE |
#
#    Then i should get user enrollments with milestone sizes:
#      | category           | program            | milestoneSize |
#      | FITNESS MANAGEMENT | Fitness Management | 1             |
#      | Weight Management  | WM Get To Know     | 1             |
#
#    Then i should get user enrollments with milestone iterations:
#      | category           | program            | iteration | status |
#      | FITNESS MANAGEMENT | Fitness Management | 1         | ACTIVE |
#      | Weight Management  | WM Get To Know     | 1         | ACTIVE |
#    And i should get user enrollments with milestone iteration reward sizes:
#      | category           | program            | iteration | rewardSize |
#      | FITNESS MANAGEMENT | Fitness Management | 1         | 1          |
#      | Weight Management  | WM Get To Know     | 1         | 0          |
#
#    And i should get user enrollments with milestone iteration activity sizes:
#      | category           | program            | iteration | activitySize |
#      | FITNESS MANAGEMENT | Fitness Management | 1         | 4            |
#      | Weight Management  | WM Get To Know     | 1         | 4            |
#
#    And i should get user enrollments with milestone iteration activities:
#      | category           | program            | iteration | activity | status | completionCount | amount | isMandatory |
#      | FITNESS MANAGEMENT | Fitness Management | 1         | BQUI1    | ACTIVE | 0               | 1      | false       |
#      | FITNESS MANAGEMENT | Fitness Management | 1         | BVID1    | ACTIVE | 0               | 1      | false       |
#      | FITNESS MANAGEMENT | Fitness Management | 1         | GSTR     | ACTIVE | 0               | 1      | true        |
#      | FITNESS MANAGEMENT | Fitness Management | 1         | GEXR     | ACTIVE | 0               | 2      | true        |
#      | Weight Management  | WM Get To Know     | 1         | BQUI1    | ACTIVE | 0               | 1      | false       |
#      | Weight Management  | WM Get To Know     | 1         | BVID1    | ACTIVE | 0               | 1      | false       |
#      | Weight Management  | WM Get To Know     | 1         | GSTR     | ACTIVE | 0               | 1      | true        |
#      | Weight Management  | WM Get To Know     | 1         | GEXR     | ACTIVE | 0               | 2      | true        |
#
#    When i complete an activity:
#      | mnemonic | txn | date |
#      | GSTR     | 1   | now  |
#    When i call user enrollments
#      | alliance | group  | branch | role   | time |
#      | string   | string | string | string | now  |
#    Then i should get user enrollments with programs:
#      | category           | program            | status | order |
#      | FITNESS MANAGEMENT | Fitness Management | ACTIVE | 1     |
#      | Weight Management  | WM Get To Know     | ACTIVE | 1     |
#    Then i should get user enrollments with milestone sizes:
#      | category           | program            | milestoneSize |
#      | FITNESS MANAGEMENT | Fitness Management | 1             |
#      | Weight Management  | WM Get To Know     | 1             |
#    Then i should get user enrollments with milestone iterations:
#      | category           | program            | iteration | status |
#      | FITNESS MANAGEMENT | Fitness Management | 1         | ACTIVE |
#      | Weight Management  | WM Get To Know     | 1         | ACTIVE |
#    And i should get user enrollments with milestone iteration activities:
#      | category           | program            | iteration | activity | status    | completionCount | amount | isMandatory |
#      | Weight Management  | WM Get To Know     | 1         | GSTR     | COMPLETED | 1               | 1      | true        |
#      | FITNESS MANAGEMENT | Fitness Management | 1         | GSTR     | COMPLETED | 1               | 1      | true        |
#
#    When i complete an activity:
#      | mnemonic | txn | date |
#      | GEXR     | 2   | now  |
#    When i call user enrollments
#
#      | alliance | group  | branch | role   | time |
#      | string   | string | string | string | now  |
#    Then i should get user enrollments with programs:
#      | category           | program            | status | order |
#      | FITNESS MANAGEMENT | Fitness Management | ACTIVE | 1     |
#      | Weight Management  | WM Get To Know     | ACTIVE | 1     |
#    Then i should get user enrollments with milestone sizes:
#      | category           | program            | milestoneSize |
#      | FITNESS MANAGEMENT | Fitness Management | 1             |
#      | Weight Management  | WM Get To Know     | 2             |
#    Then i should get user enrollments with milestone iterations:
#      | category           | program            | iteration | status    |
#      | FITNESS MANAGEMENT | Fitness Management | 1         | ACTIVE    |
#      | Weight Management  | WM Get To Know     | 1         | COMPLETED |
#      | Weight Management  | WM Get To Know     | 2         | ACTIVE    |
#    And i should get user enrollments with milestone iteration reward sizes:
#      | category           | program            | iteration | rewardSize |
#      | FITNESS MANAGEMENT | Fitness Management | 1         | 1          |
#      | Weight Management  | WM Get To Know     | 2         | 0          |
#    And i should get user enrollments with milestone iteration activities:
#      | category           | program            | iteration | activity | status | completionCount | amount | isMandatory |
#      | Weight Management  | WM Get To Know     | 1         | GEXR     | ACTIVE | 1               | 2      | true        |
#      | FITNESS MANAGEMENT | Fitness Management | 1         | GEXR     | ACTIVE | 1               | 2      | true        |
#
#      | Weight Management  | WM Get To Know     | 2         | BART1    | ACTIVE | 0               | 1      | false       |
#      | Weight Management  | WM Get To Know     | 2         | GPOS     | ACTIVE | 0               | 5      | true        |
#      | Weight Management  | WM Get To Know     | 2         | GEXR     | ACTIVE | 0               | 2      | true        |
#      | Weight Management  | WM Get To Know     | 2         | BVID2    | ACTIVE | 0               | 1      | false       |
#
#    When i complete an activity:
#      | mnemonic | txn | date |
#      | GEXR     | 2   | now  |
#    When i call user enrollments
#
#      | alliance | group  | branch | role   | time |
#      | string   | string | string | string | now  |
#    Then i should get user enrollments with programs:
#      | category           | program            | status    | order |
#      | FITNESS MANAGEMENT | Fitness Management | COMPLETED | 1     |
#      | Weight Management  | WM Get To Know     | ACTIVE    | 1     |
#    Then i should get user enrollments with programs rewards size:
#      | category           | program            | rewardSize |
#      | FITNESS MANAGEMENT | Fitness Management | 1          |
#      | Weight Management  | WM Get To Know     | 1          |
#    Then i should get user enrollments with programs rewards:
#      | category           | program            | rewardValue | rewardType              | awardDate | status    |
#      | FITNESS MANAGEMENT | Fitness Management | 2           | LIFETIME GYM MEMBERSHIP | empty     | PENDING   |
#      | Weight Management  | WM Get To Know     | 1000        | POINT                   | empty     | AVAILABLE |
#    Then i should get user enrollments with milestone sizes:
#      | category           | program            | milestoneSize |
#      | FITNESS MANAGEMENT | Fitness Management | 1             |
#      | Weight Management  | WM Get To Know     | 2             |
#    Then i should get user enrollments with milestone iterations:
#      | category           | program            | iteration | status    |
#      | FITNESS MANAGEMENT | Fitness Management | 1         | COMPLETED |
#      | Weight Management  | WM Get To Know     | 1         | COMPLETED |
#      | Weight Management  | WM Get To Know     | 2         | ACTIVE    |
#    And i should get user enrollments with milestone iteration activities:
#      | category           | program            | iteration | activity | status    | completionCount | amount | isMandatory |
#      | Weight Management  | WM Get To Know     | 1         | GEXR     | ACTIVE    | 1               | 2      | true        |
#      | FITNESS MANAGEMENT | Fitness Management | 1         | GEXR     | COMPLETED | 2               | 2      | true        |
#
#      | Weight Management  | WM Get To Know     | 2         | BART1    | ACTIVE    | 0               | 1      | false       |
#      | Weight Management  | WM Get To Know     | 2         | GPOS     | ACTIVE    | 0               | 5      | true        |
#      | Weight Management  | WM Get To Know     | 2         | GEXR     | ACTIVE    | 0               | 2      | true        |
#      | Weight Management  | WM Get To Know     | 2         | BVID2    | ACTIVE    | 0               | 1      | false       |
#
#    When i complete an activity:
#      | mnemonic | txn | date |
#      | GEXR     | 3   | 8    |
#    And i complete an activity:
#      | mnemonic | txn | date |
#      | GPOS     | 4   | 8    |
#    When i call user enrollments
#
#      | alliance | group  | branch | role   | time |
#      | string   | string | string | string | 8    |
#    And i should get user enrollments with program size:
#      | category           | programSize |
#      | FITNESS MANAGEMENT | 1           |
#      | Weight Management  | 2           |
#    Then i should get user enrollments with programs:
#      | category           | program            | status    | order |
#      | FITNESS MANAGEMENT | Fitness Management | COMPLETED | 1     |
#      | Weight Management  | WM Get To Know     | COMPLETED | 1     |
#      | Weight Management  | WM Get Ready       | ACTIVE    | 2     |
#    Then i should get user enrollments with programs rewards size:
#      | category           | program            | rewardSize |
#      | FITNESS MANAGEMENT | Fitness Management | 1          |
#      | Weight Management  | WM Get To Know     | 1          |
#      | Weight Management  | WM Get Ready       | 1          |
#    Then i should get user enrollments with programs rewards:
#      | category           | program            | rewardValue | rewardType              | awardDate | status    |
#      | FITNESS MANAGEMENT | Fitness Management | 2           | LIFETIME GYM MEMBERSHIP | empty     | PENDING   |
#      | Weight Management  | WM Get To Know     | 1000        | POINT                   | empty     | PENDING   |
#      | Weight Management  | WM Get Ready       | 500         | GIFT CARD               | empty     | AVAILABLE |
#    Then i should get user enrollments with milestone sizes:
#      | category           | program            | milestoneSize |
#      | FITNESS MANAGEMENT | Fitness Management | 1             |
#      | Weight Management  | WM Get To Know     | 2             |
#      | Weight Management  | WM Get Ready       | 1             |
#    Then i should get user enrollments with milestone iterations:
#      | category           | program            | iteration | status    |
#      | FITNESS MANAGEMENT | Fitness Management | 1         | COMPLETED |
#      | Weight Management  | WM Get To Know     | 1         | COMPLETED |
#      | Weight Management  | WM Get To Know     | 2         | COMPLETED |
#      | Weight Management  | WM Get Ready       | 1         | ACTIVE    |
#    And i should get user enrollments with milestone iteration reward sizes:
#      | category           | program            | iteration | rewardSize |
#      | FITNESS MANAGEMENT | Fitness Management | 1         | 1          |
#      | Weight Management  | WM Get Ready       | 1         | 0          |
#    And i should get user enrollments with milestone iteration activities:
#      | category          | program        | iteration | activity | status | completionCount | amount | isMandatory |
#      | Weight Management | WM Get To Know | 2         | BART1    | ACTIVE | 0               | 1      | false       |
#      | Weight Management | WM Get To Know | 2         | GPOS     | ACTIVE | 1               | 5      | true        |
#      | Weight Management | WM Get To Know | 2         | GEXR     | ACTIVE | 1               | 2      | true        |
#      | Weight Management | WM Get To Know | 2         | BVID2    | ACTIVE | 0               | 1      | false       |
#
#      | Weight Management | WM Get Ready   | 1         | BQUI1    | ACTIVE | 0               | 1      | false       |
#      | Weight Management | WM Get Ready   | 1         | BVID1    | ACTIVE | 0               | 1      | false       |
#      | Weight Management | WM Get Ready   | 1         | GSTR     | ACTIVE | 0               | 1      | true        |
#      | Weight Management | WM Get Ready   | 1         | GEXR     | ACTIVE | 0               | 2      | true        |
#
#    When i complete an activity:
#      | mnemonic | txn | date |
#      | GEXR     | 2   | 16   |
#    And i complete an activity:
#      | mnemonic | txn | date |
#      | GSTR     | 4   | 16   |
#    Then i call user enrollments
#      | alliance | group  | branch | role   | time |
#      | string   | string | string | string | 16   |
#    And i should get user enrollments with milestone iteration activities:
#      | category          | program      | iteration | activity | status    | completionCount | amount | isMandatory |
#      | Weight Management | WM Get Ready | 1         | BQUI1    | ACTIVE    | 0               | 1      | false       |
#      | Weight Management | WM Get Ready | 1         | BVID1    | ACTIVE    | 0               | 1      | false       |
#      | Weight Management | WM Get Ready | 1         | GEXR     | ACTIVE    | 1               | 2      | true        |
#      | Weight Management | WM Get Ready | 1         | GSTR     | COMPLETED | 1               | 1      | true        |
#      | Weight Management | WM Get Ready | 2         | BART1    | ACTIVE    | 0               | 1      | false       |
#      | Weight Management | WM Get Ready | 2         | GPOS     | ACTIVE    | 0               | 5      | true        |
#      | Weight Management | WM Get Ready | 2         | GEXR     | ACTIVE    | 0               | 2      | true        |
#      | Weight Management | WM Get Ready | 2         | BVID2    | ACTIVE    | 0               | 1      | false       |
#
#    When i complete an activity:
#      | mnemonic | txn | date |
#      | GEXR     | 2   | 24   |
#    And i complete an activity:
#      | mnemonic | txn | date |
#      | GPOS     | 4   | 24   |
#    Then i call user enrollments
#      | alliance | group  | branch | role   | time |
#      | string   | string | string | string | 24   |
#    And i should get user enrollments with program size:
#      | category           | programSize |
#      | FITNESS MANAGEMENT | 1           |
#      | Weight Management  | 3           |
#    Then i should get user enrollments with programs:
#      | category           | program            | status    | order |
#      | FITNESS MANAGEMENT | Fitness Management | COMPLETED | 1     |
#      | Weight Management  | WM Get To Know     | COMPLETED | 1     |
#      | Weight Management  | WM Get Ready       | COMPLETED | 2     |
#      | Weight Management  | WM Maintenance     | ACTIVE    | 3     |
#    Then i should get user enrollments with programs rewards size:
#      | category           | program            | rewardSize |
#      | FITNESS MANAGEMENT | Fitness Management | 1          |
#      | Weight Management  | WM Get To Know     | 1          |
#      | Weight Management  | WM Get Ready       | 1          |
#      | Weight Management  | WM Maintenance     | 0          |
#    Then i should get user enrollments with programs rewards:
#      | category           | program            | rewardValue | rewardType              | awardDate | status  |
#      | FITNESS MANAGEMENT | Fitness Management | 2           | LIFETIME GYM MEMBERSHIP | empty     | PENDING |
#      | Weight Management  | WM Get To Know     | 1000        | POINT                   | empty     | PENDING |
#      | Weight Management  | WM Get Ready       | 500         | GIFT CARD               | empty     | PENDING |
#    Then i should get user enrollments with milestone sizes:
#      | category           | program            | milestoneSize |
#      | FITNESS MANAGEMENT | Fitness Management | 1             |
#      | Weight Management  | WM Get To Know     | 2             |
#      | Weight Management  | WM Get Ready       | 2             |
#      | Weight Management  | WM Maintenance     | 1             |
#    And i should get user enrollments with milestone iteration reward sizes:
#      | category           | program            | iteration | rewardSize |
#      | FITNESS MANAGEMENT | Fitness Management | 1         | 1          |
#      | Weight Management  | WM Maintenance     | 1         | 1          |
#    Then i should get user enrollments with milestone iterations:
#      | category           | program            | iteration | status    |
#      | FITNESS MANAGEMENT | Fitness Management | 1         | COMPLETED |
#      | Weight Management  | WM Get To Know     | 1         | COMPLETED |
#      | Weight Management  | WM Get To Know     | 2         | COMPLETED |
#      | Weight Management  | WM Get Ready       | 1         | COMPLETED |
#      | Weight Management  | WM Get Ready       | 2         | COMPLETED |
#      | Weight Management  | WM Maintenance     | 1         | ACTIVE    |
#    And i should get user enrollments with milestone iteration activities:
#      | category          | program        | iteration | activity | status | completionCount | amount | isMandatory |
#      | Weight Management | WM Get Ready   | 2         | BART1    | ACTIVE | 0               | 1      | false       |
#      | Weight Management | WM Get Ready   | 2         | GPOS     | ACTIVE | 1               | 5      | true        |
#      | Weight Management | WM Get Ready   | 2         | GEXR     | ACTIVE | 1               | 2      | true        |
#      | Weight Management | WM Get Ready   | 2         | BVID2    | ACTIVE | 0               | 1      | false       |
#
#      | Weight Management | WM Maintenance | 1         | BQUI1    | ACTIVE | 0               | 1      | false       |
#      | Weight Management | WM Maintenance | 1         | BVID1    | ACTIVE | 0               | 1      | false       |
#      | Weight Management | WM Maintenance | 1         | GSTR     | ACTIVE | 0               | 1      | true        |
#      | Weight Management | WM Maintenance | 1         | GEXR     | ACTIVE | 0               | 2      | true        |
