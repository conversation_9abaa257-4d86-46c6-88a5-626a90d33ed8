#
## ==========================================
## File: src/test/resources/features/integration-tests.feature
## ==========================================
#
#Feature: End-to-End Journey Integration Tests
#  As a user
#  I want to complete entire journey flows
#  So that I can achieve my training objectives
#
#  Background:
#    Given the rest application has started up
#    And the database is clean
#    And the following test data is loaded:
#      | entity   | file_path                    |
#      | alliances| classpath:test-data/alliances.sql |
#      | users    | classpath:test-data/users.sql     |
#      | journeys | classpath:test-data/journeys.sql  |
#
#  @Integration
#  Scenario: Complete Alliance A user journey flow
#    Given user "alice" from "Alliance A" has completed prerequisite "CAC-A"
#    And today's date is "2025-07-01"
#    When user "alice" enrolls in "Cross Alliance Collaboration"
#    Then enrollment should be successful
#    And journey state should be "ACTIVE"
#    When the journey starts on "2025-08-15"
#    And user "alice" progresses through all milestones using Alliance A rules
#    And user "alice" completes all required activities and appointments
#    Then journey state should be "COMPLETED"
#    And user "alice" should receive completion certificate
#    And journey capacity should be reduced by 1
#
#  @Integration
#  Scenario: Complete journey flow with capacity management
#    Given journey "Limited Training" has capacity 2
#    And users "alice,bob,charlie" meet enrollment requirements
#    When users "alice,bob" enroll successfully
#    Then journey capacity should be 2/2
#    And journey should not be visible to "charlie"
#    When user "alice" withdraws
#    Then journey capacity should be 1/2
#    And journey should become visible to "charlie"
#    When user "charlie" enrolls
#    Then journey capacity should be 2/2
