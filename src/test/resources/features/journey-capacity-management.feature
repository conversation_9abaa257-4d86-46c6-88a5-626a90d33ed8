#
## ==========================================
## File: src/test/resources/features/journey-capacity-management.feature
## ==========================================
#
#Feature: Journey Capacity Management
#  As a system administrator
#  I want to manage journey enrollment capacity
#  So that resource constraints are respected
#
#  Background:
#    Given the rest application has started up
#    And today's date is "2025-06-20"
#
#  @CapacityManagement
#  Scenario: Journey becomes invisible when capacity is reached
#    Given a journey "Popular Training" exists with:
#      | capacity         | 5  |
#      | current_enrolled | 4  |
#      | enrollment_start | 2025-06-15 |
#      | enrollment_end   | 2025-06-25 |
#    And user "alice" can see "Popular Training" in available journeys
#    When user "bob" enrolls successfully in "Popular Training"
#    Then the journey capacity should be 5/5
#    And the journey "Popular Training" should not be visible to any new users
#    And user "alice" should not see "Popular Training" in available journeys
#
#  @CapacityManagement
#  Scenario: Journey becomes visible again when user withdraws
#    Given a journey "Training With Withdrawal" exists with:
#      | capacity         | 3  |
#      | current_enrolled | 3  |
#    And the journey is not visible to new users due to full capacity
#    When user "enrolled_user" withdraws from "Training With Withdrawal"
#    Then the journey capacity should be 2/3
#    And the journey "Training With Withdrawal" should become visible to eligible users
#
#  @CapacityManagement
#  Scenario: Administrative capacity increase makes journey visible
#    Given a journey "Admin Managed Training" exists with:
#      | capacity         | 10 |
#      | current_enrolled | 10 |
#    And the journey is not visible due to full capacity
#    When administrator increases capacity to 15
#    Then the journey capacity should be 10/15
#    And the journey "Admin Managed Training" should become visible to eligible users
