#
## ==========================================
## File: src/test/resources/features/multi-program-journeys.feature
## ==========================================
#
#Feature: Multi-Program Journey Management
#  As a user
#  I want to progress through multiple programs within a journey
#  So that I can complete complex training sequences
#
#  Background:
#    Given the rest application has started up
#    And user "alice" from "Alliance A" is enrolled in "Multi-Program Journey"
#    And the journey has programs:
#      | program_id | program_name    | sequence_order |
#      | 1          | Foundation     | 1              |
#      | 2          | Advanced       | 2              |
#      | 3          | Mastery        | 3              |
#
#  @MultiProgram
#  Scenario: Successful program transition with alliance-specific rules
#    Given user "alice" is in program "Foundation"
#    And alliance "Alliance A" requires completion of activities: "Basic Concepts,Tool Usage"
#    When user "alice" completes required activities
#    Then user "alice" program status should be "COMPLETED"
#    And user "alice" should automatically progress to program "Advanced"
#    And the program transition should be logged in audit trail
#
#  @MultiProgram
#  Scenario: Program transition blocked due to incomplete requirements
#    Given user "alice" is in program "Foundation"
#    And alliance "Alliance A" requires completion of activities: "Basic Concepts,Tool Usage,Assessment"
#    When user "alice" completes activities: "Basic Concepts,Tool Usage"
#    But user "alice" has not completed "Assessment"
#    Then user "alice" program status should be "IN_PROGRESS"
#    And user "alice" should remain in program "Foundation"
#    And user "alice" should see message "Complete remaining activities: Assessment"
#
