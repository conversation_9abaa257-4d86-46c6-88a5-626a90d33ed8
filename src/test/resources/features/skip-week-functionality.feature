#
## ==========================================
## File: src/test/resources/features/skip-week-functionality.feature
## ==========================================
#
#Feature: Skip Week Functionality
#  As a user enrolled in a journey
#  I want skip weeks to be handled automatically
#  So that I don't need to complete activities during break periods
#
#  Background:
#    Given the rest application has started up
#    And user "alice" is enrolled in journey "Journey With Skip Week"
#    And the journey has the following milestone structure:
#      | week | milestone_type | activities_count | appointments_count |
#      | 1    | REGULAR       | 2                | 1                  |
#      | 2    | REGULAR       | 1                | 1                  |
#      | 3    | SKIP          | 0                | 0                  |
#      | 4    | REGULAR       | 2                | 0                  |
#
#  @SkipWeek
#  Scenario: User automatically progresses through skip week
#    Given user "alice" has completed milestone for week 2
#    When the system processes milestone progression
#    Then user "alice" should be in week 3
#    And week 3 should be marked as "SKIP_WEEK"
#    And user "alice" should see message "Break week - no activities required"
#    When 7 days pass
#    Then user "alice" should automatically progress to week 4
#    And week 4 should be marked as "ACTIVE"
