## src/test/resources/features/activity_completion.feature
#
#Feature: Activity Completion Tracking
#  As a user participating in a health journey
#  I want my activities to be tracked and counted correctly
#  So that I can progress through milestones automatically
#
#  Background:
#    Given the rest application has started up
#    And set up authorized entity
#    And clean up events
#
#  Scenario: User completes a single activity occurrence
#    Given user 12345 has an active milestone with the following activities:
#      | activityMnemonic | activityName    | requiredAmount | completionCount | status |
#      | STEPS_5000       | Walk 5000 steps | 7              | 3               | ACTIVE |
#      | LOG_MEALS        | Log daily meals | 7              | 5               | ACTIVE |
#    When user 12345 completes activity "STEPS_5000" with transaction id 98765 at "2024-06-15T14:30:00"
#    Then the activity "STEPS_5000" should have completion count 4
#    And the activity "STEPS_5000" should still have status "ACTIVE"
#    And a transaction record should be created with id 98765
#
#  Scenario: User completes final occurrence of an activity
#    Given user 12345 has an active milestone with the following activities:
#      | activityMnemonic | activityName    | requiredAmount | completionCount | status |
#      | STEPS_5000       | Walk 5000 steps | 7              | 6               | ACTIVE |
#    When user 12345 completes activity "STEPS_5000" with transaction id 98766 at "2024-06-15T14:30:00"
#    Then the activity "STEPS_5000" should have completion count 7
#    And the activity "STEPS_5000" should have status "COMPLETED"
#
#  Scenario: User attempts to complete already completed activity
#    Given user 12345 has an active milestone with the following activities:
#      | activityMnemonic | activityName    | requiredAmount | completionCount | status    |
#      | STEPS_5000       | Walk 5000 steps | 7              | 7               | COMPLETED |
#    When user 12345 completes activity "STEPS_5000" with transaction id 98767 at "2024-06-15T14:30:00"
#    Then the activity "STEPS_5000" should still have completion count 7
#    And the activity "STEPS_5000" should still have status "COMPLETED"
#    And no new transaction record should be created
#
#  Scenario: User submits duplicate activity transaction
#    Given user 12345 has an active milestone with the following activities:
#      | activityMnemonic | activityName    | requiredAmount | completionCount | status |
#      | STEPS_5000       | Walk 5000 steps | 7              | 3               | ACTIVE |
#    And user 12345 has already submitted transaction id 98765 for activity "STEPS_5000"
#    When user 12345 completes activity "STEPS_5000" with transaction id 98765 at "2024-06-15T14:30:00"
#    Then the activity "STEPS_5000" should still have completion count 3
#    And no additional transaction record should be created
#
#  Scenario: Multiple activities completed in sequence
#    Given user 12345 has an active milestone with the following activities:
#      | activityMnemonic | activityName       | requiredAmount | completionCount | status |
#      | STEPS_5000       | Walk 5000 steps    | 7              | 6               | ACTIVE |
#      | LOG_MEALS        | Log daily meals    | 7              | 6               | ACTIVE |
#      | WEIGH_IN         | Weigh yourself     | 2              | 1               | ACTIVE |
#    When user 12345 completes the following activities:
#      | activityMnemonic | transactionId | completedAt         |
#      | STEPS_5000       | 98768         | 2024-06-15T14:30:00 |
#      | LOG_MEALS        | 98769         | 2024-06-15T18:45:00 |
#      | WEIGH_IN         | 98770         | 2024-06-15T08:00:00 |
#    Then the activities should have the following status:
#      | activityMnemonic | completionCount | status    |
#      | STEPS_5000       | 7               | COMPLETED |
#      | LOG_MEALS        | 7               | COMPLETED |
#      | WEIGH_IN         | 2               | COMPLETED |
#
#  Scenario: Activity completion across multiple milestones
#    Given user 12345 has multiple active milestones:
#      | milestoneId | categoryCode | iteration | status |
#      | 101         | WEIGHT_LOSS  | 2         | ACTIVE |
#      | 102         | FITNESS      | 1         | ACTIVE |
#    And milestone 101 has activity "STEPS_5000" with 3/7 completions
#    And milestone 102 has activity "STEPS_5000" with 1/5 completions
#    When user 12345 completes activity "STEPS_5000" with transaction id 98771 at "2024-06-15T14:30:00"
#    Then milestone 101 activity "STEPS_5000" should have completion count 4
#    And milestone 102 activity "STEPS_5000" should have completion count 2
#    And both milestones should receive the same transaction record
#
#  Scenario: Activity completion with invalid data
#    Given user 12345 has an active milestone
#    When user 12345 attempts to complete activity with invalid data:
#      | field            | value   | error                        |
#      |                  |         | User ID cannot be null       |
#      | activityMnemonic |         | Activity mnemonic required   |
#      | transactionId    |         | Transaction ID required      |
#      | completedAt      |         | Completion time required     |
#    Then the activity completion should be rejected
#    And appropriate error messages should be returned
#
#  Scenario: Get user's active activities
#    Given user 12345 has the following active milestones:
#      | milestoneId | categoryCode | status |
#      | 101         | WEIGHT_LOSS  | ACTIVE |
#      | 102         | FITNESS      | ACTIVE |
#    And the milestones have the following activities:
#      | milestoneId | activityMnemonic | activityName       | status    |
#      | 101         | STEPS_5000       | Walk 5000 steps    | ACTIVE    |
#      | 101         | LOG_MEALS        | Log daily meals    | COMPLETED |
#      | 102         | WORKOUT_30MIN    | 30 min workout     | ACTIVE    |
#    When user 12345 requests their active activities at "2024-06-15T10:00:00"
#    Then the response should contain 2 active activities:
#      | activityMnemonic | activityName    |
#      | STEPS_5000       | Walk 5000 steps |
#      | WORKOUT_30MIN    | 30 min workout  |
#
#  Scenario: Calculate milestone completion percentage
#    Given user 12345 has milestone 101 with the following activities:
#      | activityMnemonic | requiredAmount | completionCount | weight |
#      | STEPS_5000       | 7              | 5               | 1.0    |
#      | LOG_MEALS        | 7              | 7               | 1.0    |
#      | WEIGH_IN         | 2              | 1               | 1.0    |
#    When the system calculates completion percentage for milestone 101
#    Then the completion percentage should be approximately 72.22%
#    # (5/7 + 7/7 + 1/2) / 3 = (0.714 + 1.0 + 0.5) / 3 = 0.7222