## ==========================================
## File: src/test/resources/features/journey-enrollment.feature
## ==========================================
#
#Feature: Journey Enrollment Management
#  As a user
#  I want to enroll in journeys that I'm eligible for
#  So that I can participate in training programs
#
#  Background:
#    Given the rest application has started up
#    And today's date is "2025-06-20"
#    And user "alice" exists with alliance "Alliance A"
#    And user "alice" has completed activities: "AAT"
#
#  @Enrollment
#  Scenario: Successful enrollment within capacity and enrollment period
#    Given a journey "Alliance A Training" exists with:
#      | alliance_restriction | Alliance A           |
#      | capacity            | 50                   |
#      | current_enrolled    | 30                   |
#      | enrollment_start    | 2025-06-15           |
#      | enrollment_end      | 2025-06-25           |
#      | precondition        | AAT                  |
#    When user "alice" attempts to enroll in "Alliance A Training"
#    Then the enrollment should be successful
#    And the journey current_enrolled should be 31
#    And user "alice" should have journey state "ACTIVE" for "Alliance A Training"
#    And the enrollment audit log should record the successful enrollment
#
#  @Enrollment
#  Scenario: Enrollment rejected due to full capacity
#    Given a journey "Popular Training" exists with:
#      | capacity         | 20 |
#      | current_enrolled | 20 |
#      | enrollment_start | 2025-06-15 |
#      | enrollment_end   | 2025-06-25 |
#    When user "alice" attempts to enroll in "Popular Training"
#    Then the enrollment should fail with error "JOURNEY_AT_CAPACITY"
#    And the error message should be "Journey is at full capacity"
#    And the journey current_enrolled should remain 20
#    And the enrollment audit log should record the failed enrollment attempt
#
#  @Enrollment
#  Scenario: Enrollment rejected due to expired enrollment period
#    Given a journey "Expired Training" exists with:
#      | capacity         | 50 |
#      | current_enrolled | 10 |
#      | enrollment_start | 2025-06-01 |
#      | enrollment_end   | 2025-06-15 |
#    When user "alice" attempts to enroll in "Expired Training"
#    Then the enrollment should fail with error "ENROLLMENT_PERIOD_EXPIRED"
#    And the error message should be "Enrollment period has ended"
#    And the journey current_enrolled should remain 10
#
#  @Enrollment
#  Scenario: Enrollment rejected due to unmet preconditions
#    Given user "alice" has not completed activity "AAT"
#    And a journey "Restricted Training" exists with:
#      | precondition | AAT |
#      | capacity     | 50  |
#    When user "alice" attempts to enroll in "Restricted Training"
#    Then the enrollment should fail with error "PRECONDITION_NOT_MET"
#    And the error message should be "Required activity 'AAT' not completed"
#
#  @Enrollment
#  Scenario: Enrollment rejected due to alliance restriction
#    Given user "alice" belongs to "Alliance A"
#    And a journey "Alliance B Training" exists with:
#      | alliance_restriction | Alliance B |
#    When user "alice" attempts to enroll in "Alliance B Training"
#    Then the enrollment should fail with error "ALLIANCE_RESTRICTION"
#    And the error message should be "Journey not available for your alliance"