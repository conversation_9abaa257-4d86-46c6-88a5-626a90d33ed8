
## ==========================================
## File: src/test/resources/features/journey-visibility.feature
## ==========================================
#
#Feature: Journey Visibility Based on State and Capacity
#  As a user
#  I want to see only journeys that are available for my current situation
#  So that I'm not shown journeys I cannot enroll in
#
#  Background:
#    Given the rest application has started up
#    And today's date is "2025-06-20"
#    And the following alliances exist in the database:
#      | id | name       |
#      | 1  | Alliance A |
#      | 2  | Alliance B |
#    And the following users exist in the database:
#      | id | username | alliance_id | completed_activities    |
#      | 1  | alice    | 1           | AAT,CAC-A,MSD-A,NSBO   |
#      | 2  | bob      | 2           | BPD,CAC-B,MSD-B,NHS    |
#      | 3  | charlie  | 1           | AAT,CAC-A              |
#
#  @JourneyVisibility
#  Scenario: User sees journeys in CAN_ENROLL state only
#    Given the following journeys exist in the database:
#      | id | name                    | alliance_restriction | capacity | current_enrolled | enrollment_start | enrollment_end | start_time          |
#      | 1  | Alliance A Training     | Alliance A           | 50       | 30              | 2025-06-15       | 2025-06-25     | 2025-08-01 00:00:00 |
#      | 2  | Alliance B Training     | Alliance B           | 30       | 10              | 2025-06-15       | 2025-06-25     | 2025-08-01 00:00:00 |
#      | 3  | Cross Alliance Journey  | null                 | 100      | 99              | 2025-06-15       | 2025-06-25     | 2025-08-01 00:00:00 |
#      | 4  | Full Capacity Journey   | null                 | 20       | 20              | 2025-06-15       | 2025-06-25     | 2025-08-01 00:00:00 |
#      | 5  | Future Journey          | null                 | 50       | 0               | 2025-06-25       | 2025-07-05     | 2025-08-01 00:00:00 |
#      | 6  | Expired Journey         | null                 | 50       | 10              | 2025-06-01       | 2025-06-15     | 2025-08-01 00:00:00 |
#    When user "alice" requests available journeys
#    Then the response should contain journeys with states:
#      | journey_name           | state     | reason                           |
#      | Alliance A Training    | CAN_ENROLL| Within enrollment period, has capacity, meets preconditions |
#    And the response should not contain journeys:
#      | journey_name           | reason                           |
#      | Alliance B Training    | Different alliance               |
#      | Cross Alliance Journey | At full capacity                 |
#      | Full Capacity Journey  | At full capacity                 |
#      | Future Journey         | Enrollment not yet started       |
#      | Expired Journey        | Enrollment period expired        |
#
#  @JourneyVisibility
#  Scenario: Full capacity journeys are not visible to users
#    Given journey "Cross Alliance Journey" has capacity 100 and current enrollment 100
#    When user "alice" requests available journeys
#    Then the journey "Cross Alliance Journey" should not be in the response
#    And the user should not see any reference to full capacity journeys