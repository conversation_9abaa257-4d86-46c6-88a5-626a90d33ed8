## src/test/resources/features/journey_enrollment.feature
#
#Feature: Journey Enrollment Management
#  As a user of the health platform
#  I want to be enrolled in appropriate journey categories
#  So that I can participate in personalized health programs
#
#  Background:
#    Given the rest application has started up
#    And set up authorized entity
#    And clean up events
#
#  Scenario: New user gets recommended and enrolled in weight loss journey
#    Given a user with id 12345 has the following profile:
#      | bmi             | 28.5      |
#      | age             | 35        |
#      | hasHealthGoals  | true      |
#      | riskFactors     | OBESITY   |
#    And the following journey categories are available:
#      | categoryCode    | name           | categoryType | enrollmentStart | enrollmentEnd   |
#      | WEIGHT_LOSS     | Weight Loss    | HEALTH       | 2024-01-01      | 2024-12-31     |
#      | FITNESS         | Fitness        | WELLNESS     | 2024-01-01      | 2024-12-31     |
#    And the weight loss category has the following programs:
#      | programId | name                    | order | duration | milestoneUnit |
#      | 1         | Weight Loss - Beginner  | 1     | 8        | WEEKS         |
#      | 2         | Weight Loss - Advanced  | 2     | 12       | WEEKS         |
#    When user 12345 requests their journey enrollments at "2024-06-15T10:00:00"
#    Then the system should recommend the following categories:
#      | categoryCode |
#      | WEIGHT_LOSS  |
#    And the user should be enrolled in program "Weight Loss - Beginner"
#    And a milestone should be created with the following activities:
#      | activityMnemonic | activityName        | requiredAmount | flexibility |
#      | STEPS_5000       | Walk 5000 steps     | 7              | FLEXIBLE    |
#      | LOG_MEALS        | Log daily meals     | 7              | STRICT      |
#      | WEIGH_IN         | Weigh yourself      | 2              | FLEXIBLE    |
#
#  Scenario: User with existing enrollment gets milestone progression
#    Given user 12345 has an active enrollment in "Weight Loss - Beginner"
#    And user 12345 has completed milestone 1 with the following activities:
#      | activityMnemonic | completionCount | requiredAmount | status    |
#      | STEPS_5000       | 7               | 7              | COMPLETED |
#      | LOG_MEALS        | 7               | 7              | COMPLETED |
#      | WEIGH_IN         | 2               | 2              | COMPLETED |
#    When user 12345 requests their journey enrollments at "2024-06-22T10:00:00"
#    Then milestone 1 should be marked as "COMPLETED"
#    And milestone 2 should be created with status "ACTIVE"
#    And the user's enrollment status should be "ACTIVE"
#
#  Scenario: User enrollment is blocked due to closed enrollment window
#    Given a journey category "WEIGHT_LOSS" with enrollment window:
#      | enrollmentStart | enrollmentEnd |
#      | 2024-01-01      | 2024-06-30    |
#    And user 12345 qualifies for the weight loss category
#    When user 12345 requests their journey enrollments at "2024-07-15T10:00:00"
#    Then the user should not be enrolled in any programs
#    And no milestones should be created
#
#  Scenario: User enrollment is blocked due to unmet preconditions
#    Given a journey category "DIABETES_MGMT" with precondition "hasHealthCondition == 'DIABETES'"
#    And user 12345 has the following profile:
#      | hasHealthCondition | NONE |
#    When user 12345 requests their journey enrollments at "2024-06-15T10:00:00"
#    Then the user should not be enrolled in "DIABETES_MGMT"
#    And the enrollment should be blocked due to "precondition not met"
#
#  Scenario: User receives multiple category recommendations
#    Given user 12345 has a profile that qualifies for multiple categories:
#      | bmi             | 28.5      |
#      | age             | 35        |
#      | hasHealthGoals  | true      |
#      | activityLevel   | LOW       |
#    And the following categories are available:
#      | categoryCode | name        |
#      | WEIGHT_LOSS  | Weight Loss |
#      | FITNESS      | Fitness     |
#    When user 12345 requests their journey enrollments at "2024-06-15T10:00:00"
#    Then the system should recommend the following categories:
#      | categoryCode |
#      | WEIGHT_LOSS  |
#      | FITNESS      |
#    And the user should have enrollments in both categories
#
#  Scenario: Retrieve user's detailed enrollment information
#    Given user 12345 has active enrollments in the following categories:
#      | categoryCode | programName             | enrollmentDate | status |
#      | WEIGHT_LOSS  | Weight Loss - Beginner  | 2024-06-01     | ACTIVE |
#      | FITNESS      | Fitness - Starter       | 2024-06-01     | ACTIVE |
#    And user 12345 has the following active milestones:
#      | categoryCode | iteration | status | milestoneFrom | milestoneTo |
#      | WEIGHT_LOSS  | 2         | ACTIVE | 2024-06-08    | 2024-06-15  |
#      | FITNESS      | 1         | ACTIVE | 2024-06-01    | 2024-06-08  |
#    When user 12345 requests detailed enrollment information at "2024-06-10T10:00:00"
#    Then the response should contain detailed enrollment information for 2 categories
#    And each category should include:
#      | field            | type   |
#      | categoryId       | number |
#      | categoryName     | string |
#      | categoryType     | string |
#      | journeyPrograms  | array  |
#      | currentMilestone | object |