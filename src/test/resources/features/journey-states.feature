## ==========================================
## File: src/test/resources/features/journey-states.feature
## ==========================================
#
#Feature: Journey State Management
#  As a user
#  I want to see my journeys organized by their current state
#  So that I understand what actions I can take
#
#  Background:
#    Given the rest application has started up
#    And today's date is "2025-06-20"
#    And user "alice" exists with alliance "Alliance A"
#    And user "alice" has completed activities: "AAT,CAC-A,MSD-A"
#
#  @JourneyStates
#  Scenario Outline: Journey states based on enrollment and completion status
#    Given a journey exists with the following configuration:
#      | name             | <journey_name>        |
#      | capacity         | <capacity>            |
#      | current_enrolled | <current_enrolled>    |
#      | enrollment_start | <enrollment_start>    |
#      | enrollment_end   | <enrollment_end>      |
#      | start_time       | <start_time>          |
#    And user "alice" enrollment status is "<user_enrollment_status>"
#    When user "alice" requests journey states
#    Then journey "<journey_name>" should have state "<expected_state>"
#
#    Examples:
#      | journey_name    | capacity | current_enrolled | enrollment_start | enrollment_end | start_time          | user_enrollment_status | expected_state |
#      | Future Journey  | 50       | 10              | 2025-06-25       | 2025-07-05     | 2025-08-01 00:00:00 | not_enrolled          | UPCOMING       |
#      | Open Journey    | 50       | 10              | 2025-06-15       | 2025-06-25     | 2025-08-01 00:00:00 | not_enrolled          | CAN_ENROLL     |
#      | Expired Journey | 50       | 10              | 2025-06-01       | 2025-06-15     | 2025-08-01 00:00:00 | not_enrolled          | EXPIRED        |
#      | Active Journey  | 50       | 10              | 2025-06-15       | 2025-06-25     | 2025-06-01 00:00:00 | enrolled              | ACTIVE         |
#      | Done Journey    | 50       | 10              | 2025-05-01       | 2025-05-15     | 2025-06-01 00:00:00 | completed             | COMPLETED      |
#
#  @JourneyStates
#  Scenario: User views their journey dashboard organized by state
#    Given the following journeys exist for user "alice":
#      | journey_name     | state     |
#      | Training Alpha   | UPCOMING  |
#      | Training Beta    | CAN_ENROLL|
#      | Training Gamma   | EXPIRED   |
#      | Training Delta   | ACTIVE    |
#      | Training Epsilon | COMPLETED |
#    When user "alice" requests their journey dashboard
#    Then the response should be organized as:
#      | state      | journey_names            |
#      | ACTIVE     | Training Delta           |
#      | CAN_ENROLL | Training Beta            |
#      | UPCOMING   | Training Alpha           |
#      | COMPLETED  | Training Epsilon         |
#      | EXPIRED    | Training Gamma           |
