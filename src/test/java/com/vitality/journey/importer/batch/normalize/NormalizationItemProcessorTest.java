package com.vitality.journey.importer.batch.normalize;

import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.model.imprt.RecordStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertSame;

class NormalizationItemProcessorTest {

    private NormalizationItemProcessor processor;

    @BeforeEach
    void setUp() {
        processor = new NormalizationItemProcessor();
    }

    @Test
    void shouldPassThroughLoadedRecords() {
        // Given
        StagingRecord stagingRecord = new StagingRecord();
        stagingRecord.setId(1L);
        stagingRecord.setStatus(RecordStatus.LOADED.name());

        // When
        StagingRecord result = processor.process(stagingRecord);

        // Then
        assertSame(stagingRecord, result);
    }

    @Test
    void shouldSkipNonLoadedRecords() {
        // Given
        StagingRecord stagingRecord = new StagingRecord();
        stagingRecord.setStatus(RecordStatus.ERROR.name());

        // When
        StagingRecord result = processor.process(stagingRecord);

        // Then
        assertNull(result);
    }
}