package com.vitality.journey.importer.batch.normalize;

import com.vitality.journey.importer.batch.BaseBatchTest;
import com.vitality.journey.importer.batch.ImportJobStep;
import com.vitality.journey.importer.batch.JobParametersFactory;
import com.vitality.journey.importer.database.databaseMapping.Member;
import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.database.databaseMapping.StagingRecordError;
import com.vitality.journey.importer.database.databaseMapping.StagingToEntityCrossmap;
import com.vitality.journey.importer.helper.TestFile;
import com.vitality.journey.importer.model.csv.DppCsvHeader;
import com.vitality.journey.importer.model.imprt.RecordStatus;
import com.vitality.journey.importer.service.imprt.staging.StagingRecordReaderException;
import com.vitality.journey.importer.util.JdbcLobUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
class NormalizationStepTest extends BaseBatchTest {

    @Autowired
    @Qualifier("stagingStep")
    private Step stagingStep;

    @Autowired
    @Qualifier("normalizeStep")
    private Step normalizeStep;

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @BeforeEach
    void setUp() {
        jobRepositoryTestUtils.removeJobExecutions();
    }

    @Test
    void testNormalizationStepWithValidStagingRecords() throws Exception {
        // Given - Execute full job to ensure proper job execution context
        int numberOfUsers = 5;
        int numberOfWeeks = 26;
        Long employerId = 100L;

        try (var tempFile = TestFile.create(csvHelper.getDppCsvFile(numberOfUsers, numberOfWeeks))) {
            // Create custom job with only staging and normalization steps
            Job testJob = new JobBuilder("testNormalizationJob", jobRepository)
                .start(stagingStep)
                .next(normalizeStep)
                .build();

            // Set the test job and execute
            jobLauncherTestUtils.setJob(testJob);

            JobParameters jobParams = JobParametersFactory.createImportJobParameters(tempFile.getName(), tempFile.path(), employerId);

            JobExecution jobExecution = jobLauncherTestUtils.launchJob(jobParams);

            // Then - Verify job completed successfully
            assertThat(jobExecution.getExitStatus()).isEqualTo(ExitStatus.COMPLETED);
            assertThat(jobExecution.getStepExecutions()).hasSize(2); // Only staging and normalize steps

            // Verify staging step executed successfully
            validateSuccessfulStepExecution(jobExecution, ImportJobStep.STAGE, numberOfUsers);

            // Verify normalization step executed successfully
            validateSuccessfulStepExecution(jobExecution, ImportJobStep.NORMALIZE, numberOfUsers);

            // Verify staging records were updated to NORMALIZED status
            List<StagingRecord> normalizedRecords = fetchStagingRecordsForJob(jobExecution.getJobId(), RecordStatus.NORMALIZED);

            assertThat(normalizedRecords).hasSize(numberOfUsers);

            normalizedRecords.forEach(stagingRecord -> {
                    assertThat(stagingRecord.getStatus()).isEqualTo(RecordStatus.NORMALIZED.name());
                    assertThat(stagingRecord.getStagingRecordErrors()).isEmpty();
                }
            );

            // Verify member crossmap entries were created
            List<StagingToEntityCrossmap> memberCrossmapEntries = entityManager.createQuery(
                    "SELECT c FROM StagingToEntityCrossmap c " +
                        "WHERE c.id.importJobId = :importJobId AND c.id.entityRefType = 'MEMBER'", StagingToEntityCrossmap.class)
                .setParameter("importJobId", jobExecution.getJobId())
                .getResultList();

            assertThat(memberCrossmapEntries).hasSize(numberOfUsers);
            memberCrossmapEntries.forEach(crossmapEntry -> {
                assertThat(crossmapEntry.getId().getImportJobId()).isEqualTo(jobExecution.getJobId());
                assertThat(crossmapEntry.getId().getStagingRecordId()).isPositive();
                assertThat(crossmapEntry.getId().getEntityRefId()).isPositive();
                assertThat(crossmapEntry.getId().getEntityRefType()).isEqualTo("MEMBER");
            });

            // Verify members were created
            List<Member> members = entityManager.createQuery("SELECT m FROM Member m WHERE m.id IN :memberIds", Member.class)
                .setParameter("memberIds", memberCrossmapEntries.stream().map(c -> c.getId().getEntityRefId()).toList())
                .getResultList();
            assertThat(members).hasSize(numberOfUsers);

            members.forEach(member -> {
                assertThat(member.getUniqueId()).isNotEmpty();
                assertThat(member.getFirstName()).isNotEmpty();
                assertThat(member.getLastName()).isNotEmpty();
                assertThat(member.getEntityNo()).isNull();
                assertThat(member.getCreatedAt()).isNotNull();
                assertThat(member.getUpdatedAt()).isNotNull();
                assertThat(member.getEmployerId()).isEqualTo(employerId);
            });
        }
    }

    @Test
    void testNormalizationStepWithInvalidJsonPayload() throws Exception {
        // Given - Create staging records with invalid JSON
        Long employerId = 100L;

        try (var tempFile = TestFile.create(csvHelper.getDppCsvFile(2, 26))) {
            // Create corruption tasklet to run between staging and normalization
            Tasklet corruptionTasklet = (contribution, chunkContext) -> {
                Long jobId = chunkContext.getStepContext().getStepExecution().getJobExecution().getJobId();
                List<StagingRecord> stagingRecords = fetchStagingRecordsForJob(jobId);
                if (!stagingRecords.isEmpty()) {
                    StagingRecord recordToCorrupt = stagingRecords.getFirst();
                    recordToCorrupt.setPayload(JdbcLobUtils.toClob("{ invalid json payload"));
                    stagingRecordRepository.save(recordToCorrupt);
                }
                return RepeatStatus.FINISHED;
            };

            Step corruptionStep = new StepBuilder("corruptionStep", jobRepository)
                .tasklet(corruptionTasklet, transactionManager)
                .build();

            // Create job with staging, corruption, and normalization steps
            Job testJob = new JobBuilder("testNormalizationInvalidJson", jobRepository)
                .start(stagingStep)
                .next(corruptionStep)
                .next(normalizeStep)
                .build();

            jobLauncherTestUtils.setJob(testJob);
            JobParameters jobParams = JobParametersFactory.createImportJobParameters(tempFile.getName(), tempFile.path(), employerId);

            JobExecution jobExecution = jobLauncherTestUtils.launchJob(jobParams);

            // Then - Job should complete with skipped records
            assertThat(jobExecution.getExitStatus()).isEqualTo(ExitStatus.COMPLETED.addExitDescription("Failed to process 1 records"));
            assertThat(jobExecution.getStepExecutions()).hasSize(3); // staging + corruption + normalize

            // Verify normalization step statistics
            jobExecution.getStepExecutions().stream()
                .filter(step -> step.getStepName().equals(ImportJobStep.NORMALIZE.getStepName()))
                .findFirst()
                .ifPresent(stepExecution -> {
                    assertThat(stepExecution.getReadCount()).isEqualTo(2);
                    assertThat(stepExecution.getWriteCount()).isEqualTo(1); // Only 1 valid record processed
                    assertThat(stepExecution.getSkipCount()).isEqualTo(1); // 1 record skipped
                });

            // Verify error was captured
            List<StagingRecord> errorRecords = fetchStagingRecordsForJob(jobExecution.getJobId(), RecordStatus.ERROR);
            assertThat(errorRecords).hasSize(1);

            StagingRecord errorRecord = errorRecords.getFirst();
            assertThat(errorRecord.getStagingRecordErrors()).hasSize(1);
            StagingRecordError stagingRecordError = CollectionUtils.firstElement(errorRecord.getStagingRecordErrors());
            assertThat(stagingRecordError).isNotNull();
            assertThat(stagingRecordError.getErrorMessage()).isNotNull();
        }
    }

    @Test
    void testNormalizationStepWithMissingRequiredFields() throws Exception {
        // Given - Create staging record with missing required fields
        Long employerId = 100L;
        int numberOfUsers = 1;

        try (var tempFile = TestFile.create(csvHelper.getDppCsvFile(numberOfUsers, 26))) {
            // Create modification tasklet to run between staging and normalization
            Tasklet modificationTasklet = (contribution, chunkContext) -> {
                Long jobId = chunkContext.getStepContext().getStepExecution().getJobExecution().getJobId();
                List<StagingRecord> stagingRecords = fetchStagingRecordsForJob(jobId);
                if (!stagingRecords.isEmpty()) {
                    StagingRecord recordToModify = stagingRecords.getFirst();
                    // Create JSON with missing firstName (required field)
                    String invalidJson = String.format("{\"%s\":\"MK123456\",\"%s\":\"Doe\",\"%s\":\"Test Class\"}",
                        DppCsvHeader.UNIQUE_ID, DppCsvHeader.LAST_NAME, DppCsvHeader.CLASS);
                    recordToModify.setPayload(JdbcLobUtils.toClob(invalidJson));
                    stagingRecordRepository.save(recordToModify);
                }
                return RepeatStatus.FINISHED;
            };

            Step modificationStep = new StepBuilder("modificationStep", jobRepository)
                .tasklet(modificationTasklet, transactionManager)
                .build();

            // Create job with staging, modification, and normalization steps
            Job testJob = new JobBuilder("testNormalizationMissingFields", jobRepository)
                .start(stagingStep)
                .next(modificationStep)
                .next(normalizeStep)
                .build();

            jobLauncherTestUtils.setJob(testJob);
            JobParameters jobParams = JobParametersFactory.createImportJobParameters(tempFile.getName(), tempFile.path(), employerId);

            JobExecution jobExecution = jobLauncherTestUtils.launchJob(jobParams);

            // Then - Job should complete with skipped record
            assertThat(jobExecution.getExitStatus().getExitCode()).isEqualTo(ExitStatus.FAILED.getExitCode());
            assertThat(jobExecution.getExitStatus().getExitDescription())
                .contains("All " + numberOfUsers + " records failed; step normalizeStep is exitCode=FAILED");
            assertThat(jobExecution.getStepExecutions()).hasSize(3); // staging + modification + normalize

            // Verify normalization step statistics
            jobExecution.getStepExecutions().stream()
                .filter(step -> step.getStepName().equals(ImportJobStep.NORMALIZE.getStepName()))
                .findFirst()
                .ifPresent(stepExecution -> {
                    assertThat(stepExecution.getReadCount()).isEqualTo(numberOfUsers);
                    assertThat(stepExecution.getWriteCount()).isZero(); // No records processed
                    assertThat(stepExecution.getSkipCount()).isEqualTo(numberOfUsers); // 1 record skipped
                });

            // Verify error was captured
            List<StagingRecord> errorRecords = fetchStagingRecordsForJob(jobExecution.getJobId(), RecordStatus.ERROR);
            assertThat(errorRecords).hasSize(numberOfUsers);

            StagingRecord errorRecord = errorRecords.getFirst();
            assertThat(errorRecord.getStagingRecordErrors()).hasSize(numberOfUsers);

            StagingRecordError stagingRecordError = CollectionUtils.firstElement(errorRecord.getStagingRecordErrors());
            assertThat(stagingRecordError).isNotNull();
            assertThat(stagingRecordError.getErrorMessage()).isNotNull();

            String errorMessage = JdbcLobUtils.readClobAsString(stagingRecordError.getErrorMessage());
            assertThat(errorMessage).contains(StagingRecordReaderException.class.getName()); // Should mention missing field
        }
    }

    @Test
    void testNormalizationStepWithMixedValidInvalidRecords() throws Exception {
        // Given - Create multiple staging records with mix of valid and invalid data
        Long employerId = 100L;
        int numberOfUsers = 3;

        try (var tempFile = TestFile.create(csvHelper.getDppCsvFile(numberOfUsers, 26))) {
            // Create mixed corruption tasklet to run between staging and normalization
            Tasklet mixedCorruptionTasklet = (contribution, chunkContext) -> {
                Long jobId = chunkContext.getStepContext().getStepExecution().getJobExecution().getJobId();
                List<StagingRecord> stagingRecords = fetchStagingRecordsForJob(jobId);
                if (stagingRecords.size() >= numberOfUsers) {
                    // Corrupt first record with invalid JSON
                    StagingRecord firstRecord = stagingRecords.getFirst();
                    firstRecord.setPayload(JdbcLobUtils.toClob("{ invalid json"));
                    stagingRecordRepository.save(firstRecord);

                    // Corrupt second record with missing required field
                    StagingRecord secondRecord = stagingRecords.get(1);
                    String invalidJson = String.format("{\"%s\":\"MK123456\",\"%s\":\"Doe\",\"%s\":\"Test Class\"}",
                        DppCsvHeader.UNIQUE_ID, DppCsvHeader.LAST_NAME, DppCsvHeader.CLASS);
                    secondRecord.setPayload(JdbcLobUtils.toClob(invalidJson));
                    stagingRecordRepository.save(secondRecord);

                    // Leave third record valid (no changes needed)
                }
                return RepeatStatus.FINISHED;
            };

            Step mixedCorruptionStep = new StepBuilder("mixedCorruptionStep", jobRepository)
                .tasklet(mixedCorruptionTasklet, transactionManager)
                .build();

            // Create job with staging, mixed corruption, and normalization steps
            Job testJob = new JobBuilder("testNormalizationMixedRecords", jobRepository)
                .start(stagingStep)
                .next(mixedCorruptionStep)
                .next(normalizeStep)
                .build();

            jobLauncherTestUtils.setJob(testJob);
            JobParameters jobParams = JobParametersFactory.createImportJobParameters(tempFile.getName(), tempFile.path(), employerId);

            JobExecution jobExecution = jobLauncherTestUtils.launchJob(jobParams);

            // Then - Job should complete with partial success
            assertThat(jobExecution.getExitStatus().getExitCode()).isEqualTo(ExitStatus.COMPLETED.getExitCode());
            assertThat(jobExecution.getExitStatus().getExitDescription())
                .contains("Failed to process 2 records");
            assertThat(jobExecution.getStepExecutions()).hasSize(numberOfUsers); // staging + mixed corruption + normalize

            // Verify normalization step statistics
            jobExecution.getStepExecutions().stream()
                .filter(step -> step.getStepName().equals(ImportJobStep.NORMALIZE.getStepName()))
                .forEach(stepExecution -> {
                    assertThat(stepExecution.getReadCount()).isEqualTo(numberOfUsers);
                    assertThat(stepExecution.getWriteCount()).isEqualTo(1); // Only 1 valid record processed
                    assertThat(stepExecution.getSkipCount()).isEqualTo(2); // 2 records skipped
                });

            // Verify one record was normalized successfully
            List<StagingRecord> normalizedRecords = fetchStagingRecordsForJob(jobExecution.getJobId(), RecordStatus.NORMALIZED);
            assertThat(normalizedRecords).hasSize(1);

            // Verify two records have errors
            List<StagingRecord> errorRecords = fetchStagingRecordsForJob(jobExecution.getJobId(), RecordStatus.ERROR);
            assertThat(errorRecords).hasSize(2);

            // Verify each error record has error details
            errorRecords.forEach(errorRecord -> {
                assertThat(errorRecord.getStagingRecordErrors()).hasSize(1);
                StagingRecordError stagingRecordError = CollectionUtils.firstElement(errorRecord.getStagingRecordErrors());
                assertThat(stagingRecordError).isNotNull();
                assertThat(stagingRecordError.getErrorMessage()).isNotNull();
            });

            // Verify one member was created for the valid record
            List<StagingToEntityCrossmap> memberCrossmapEntries = entityManager.createQuery(
                    "SELECT c FROM StagingToEntityCrossmap c " +
                        "WHERE c.id.importJobId = :importJobId AND c.id.entityRefType = 'MEMBER'", StagingToEntityCrossmap.class)
                .setParameter("importJobId", jobExecution.getJobId())
                .getResultList();
            assertThat(memberCrossmapEntries).hasSize(1);
        }
    }

    private static void validateSuccessfulStepExecution(JobExecution jobExecution, ImportJobStep stage, int numberOfUsers) {
        jobExecution.getStepExecutions().stream()
            .filter(step -> step.getStepName().equals(stage.getStepName()))
            .forEach(stepExecution -> {
                assertThat(stepExecution.getExitStatus()).isEqualTo(ExitStatus.COMPLETED);
                assertThat(stepExecution.getReadCount()).isEqualTo(numberOfUsers);
                assertThat(stepExecution.getWriteCount()).isEqualTo(numberOfUsers);
                assertThat(stepExecution.getSkipCount()).isZero();
            });
    }
}
