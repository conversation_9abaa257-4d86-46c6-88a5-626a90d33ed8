package com.vitality.journey.importer.batch.stage;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vitality.journey.importer.batch.BaseBatchTest;
import com.vitality.journey.importer.batch.ImportJobStep;
import com.vitality.journey.importer.batch.JobParametersFactory;
import com.vitality.journey.importer.database.databaseMapping.StagingRecord;
import com.vitality.journey.importer.helper.TestFile;
import com.vitality.journey.importer.model.csv.DppCsvHeader;
import com.vitality.journey.importer.model.csv.DppMemberRecord;
import com.vitality.journey.importer.model.imprt.RecordStatus;
import com.vitality.journey.importer.util.CustomMediaTypes;
import com.vitality.journey.importer.util.JdbcLobUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;

import java.io.IOException;
import java.nio.file.Files;
import java.sql.Clob;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
class StagingStepTest extends BaseBatchTest {

    @Autowired
    @Qualifier("stagingStep")
    private Step stagingStep;

    @Autowired
    private JobRepository jobRepository;

    @BeforeEach
    void setUp() {
        jobRepositoryTestUtils.removeJobExecutions();
    }

    @Test
    void testStagingStepWithValidCsvData() throws Exception {
        int numberOfUsers = 10;
        int numberOfWeeks = 26;

        try (var tempFile = TestFile.create(csvHelper.getDppCsvFile(numberOfUsers, numberOfWeeks))) {
            // Create custom job with only staging step
            Job testJob = new JobBuilder("testStagingJob", jobRepository)
                    .start(stagingStep)
                    .build();

            jobLauncherTestUtils.setJob(testJob);

            JobParameters params = JobParametersFactory.createImportJobParameters(tempFile.path());

            JobExecution jobExecution = jobLauncherTestUtils.launchJob(params);

            assertThat(jobExecution.getExitStatus()).isEqualTo(ExitStatus.COMPLETED);
            assertThat(jobExecution.getStepExecutions()).hasSize(1); // Only staging step

            jobExecution.getStepExecutions().forEach(stepExecution -> {
                assertThat(stepExecution.getStepName()).isEqualTo(ImportJobStep.STAGE.getStepName());
                assertThat(stepExecution.getExitStatus()).isEqualTo(ExitStatus.COMPLETED);
                assertThat(stepExecution.getReadCount()).isEqualTo(numberOfUsers);
                assertThat(stepExecution.getWriteCount()).isEqualTo(numberOfUsers);
            });

            List<StagingRecord> stagingRecords = fetchStagingRecordsForJob(jobExecution.getJobId());

            assertThat(stagingRecords).hasSize(numberOfUsers);

            for (StagingRecord stagingRecord : stagingRecords) {
                assertThat(stagingRecord.getStatus()).isEqualTo(RecordStatus.LOADED.name());
                assertThat(stagingRecord.getType()).isEqualTo(MediaType.APPLICATION_JSON.getSubtype());

                Clob payload = stagingRecord.getPayload();
                assertThat(payload).isNotNull();

                String json = JdbcLobUtils.readClobAsString(payload);
                assertThat(json).isNotEmpty();
                assertThat(isJson(json)).isTrue();
            }
        }
    }

    @Test
    void testStagingStepWithInvalidCsvRow() throws Exception {
        int numberOfWeeks = 26;
        String className = "Test Class " + System.currentTimeMillis();

        // Create CSV with valid and invalid rows
        List<DppMemberRecord> memberRecords = dataGenerator.generate(2, numberOfWeeks, className);
        String validRow1 = csvWriter.toCsvLine(memberRecords.get(0));
        String validRow2 = csvWriter.toCsvLine(memberRecords.get(1));
        String invalidRow = String.format("MK382527,\"\"\"''',Robin,Ebert,%s,20241026,20250117,20250412", className);

        String csv = DppCsvHeader.buildHeader(numberOfWeeks) + "\n" +
                validRow1 + "\n" +
                invalidRow + "\n" +
                validRow2 + "\n";

        try (var tempFile = TestFile.create(Files.createTempFile("test-invalid-", ".csv"))) {
            Files.write(tempFile.path(), csv.getBytes());

            // Create custom job with only staging step
            Job testJob = new JobBuilder("testStagingInvalidJob", jobRepository)
                    .start(stagingStep)
                    .build();

            jobLauncherTestUtils.setJob(testJob);

            JobParameters params = JobParametersFactory.createImportJobParameters(tempFile.path());

            JobExecution jobExecution = jobLauncherTestUtils.launchJob(params);

            assertThat(jobExecution.getExitStatus()).isEqualTo(ExitStatus.COMPLETED);

            List<StagingRecord> stagingRecords = fetchStagingRecordsForJob(jobExecution.getJobId());
            assertThat(stagingRecords).hasSize(3);

            // Verify error record
            StagingRecord errorRecord = stagingRecords.stream()
                    .filter(stagingRecord -> stagingRecord.getStatus().equals(RecordStatus.ERROR.name()))
                    .findFirst()
                    .orElseThrow();

            assertThat(errorRecord.getType()).isEqualTo(CustomMediaTypes.TEXT_CSV.getSubtype());
            assertThat(isJson(JdbcLobUtils.readClobAsString(errorRecord.getPayload()))).isFalse();
            assertThat(errorRecord.getStagingRecordErrors()).hasSize(1);

            errorRecord.getStagingRecordErrors().forEach(error -> {
                assertThat(error.getErrorMessage()).isNotNull();
                String stacktrace = JdbcLobUtils.readClobAsString(error.getErrorMessage());
                assertThat(stacktrace).isNotEmpty();
                assertThat(stacktrace).contains("Incorrect number of tokens");
            });

            // Verify valid records
            long validRecords = stagingRecords.stream()
                    .filter(stagingRecord -> stagingRecord.getStatus().equals(RecordStatus.LOADED.name()))
                    .count();
            assertThat(validRecords).isEqualTo(2);
        }
    }

    @Test
    void testStagingStepWithEmptyFile() throws Exception {
        try (var tempFile = TestFile.create(Files.createTempFile("test-empty-", ".csv"))) {
            // File is created but remains empty

            // Create custom job with only staging step
            Job testJob = new JobBuilder("testStagingEmptyJob", jobRepository)
                    .start(stagingStep)
                    .build();

            jobLauncherTestUtils.setJob(testJob);

            JobParameters params = JobParametersFactory.createImportJobParameters(tempFile.path());

            JobExecution jobExecution = jobLauncherTestUtils.launchJob(params);

            assertThat(jobExecution.getExitStatus()).isEqualTo(ExitStatus.COMPLETED);

            // Verify no records were created
            jobExecution.getStepExecutions().forEach(stepExecution -> {
                assertThat(stepExecution.getReadCount()).isZero();
                assertThat(stepExecution.getWriteCount()).isZero();
            });

            long count = stagingRecordRepository.count();
            assertThat(count).isZero();
        }
    }

    @Test
    void testStagingStepWithCorruptedFileContent() throws Exception {
        // Create file with binary/corrupted content
        byte[] corruptedContent = {(byte) 0xFF, (byte) 0xFE, (byte) 0x00, (byte) 0x01,
                (byte) 0x89, (byte) 0x50, (byte) 0x4E, (byte) 0x47};

        try (var tempFile = TestFile.create(Files.createTempFile("test-corrupted-", ".csv"))) {
            Files.write(tempFile.path(), corruptedContent);

            // Create custom job with only staging step
            Job testJob = new JobBuilder("testStagingCorruptedJob", jobRepository)
                    .start(stagingStep)
                    .build();

            jobLauncherTestUtils.setJob(testJob);

            JobParameters params = JobParametersFactory.createImportJobParameters(tempFile.path());

            JobExecution jobExecution = jobLauncherTestUtils.launchJob(params);

            assertThat(jobExecution.getExitStatus().getExitCode()).isEqualTo(ExitStatus.FAILED.getExitCode());
            assertThat(jobExecution.getExitStatus().getExitDescription()).isNotEmpty();
            assertThat(jobExecution.getStepExecutions()).hasSize(1);

            // Verify no records were created (corrupted content results in empty processing)
            jobExecution.getStepExecutions().forEach(stepExecution -> {
                assertThat(stepExecution.getReadCount()).isZero();
                assertThat(stepExecution.getWriteCount()).isZero();
            });

            long count = stagingRecordRepository.count();
            assertThat(count).isZero();
        }
    }

    private boolean isJson(String json) {
        try {
            new ObjectMapper().readTree(json);
            return true;
        } catch (IOException e) {
            return false;
        }
    }
}
