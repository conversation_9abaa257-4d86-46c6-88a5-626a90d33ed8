// package za.co.discovery.health.journey.steps;
//
// import io.cucumber.datatable.DataTable;
// import io.cucumber.java.en.And;
// import io.cucumber.java.en.Then;
// import io.cucumber.java.en.When;
// import lombok.extern.slf4j.Slf4j;
// import org.junit.Assert;
// import org.springframework.beans.factory.annotation.Autowired;
// import za.co.discovery.health.journey.SpringIntegrationTest;
// import za.co.discovery.health.journey.model.user.UserCategoryEnrollmentDto;
// import za.co.discovery.health.journey.model.user.UserMilestoneEnrollmentDto;
// import za.co.discovery.health.journey.model.user.UserProgramEnrollmentDto;
// import za.co.discovery.health.journey.service.journey.JourneyActivityService;
// import za.co.discovery.health.journey.service.journey.JourneyProcessor;
// import za.co.discovery.health.journey.world.JourneyWorld;
//
// import java.time.LocalDateTime;
// import java.util.List;
// import java.util.Map;
// import java.util.Objects;
// import java.util.Optional;
//
// import static za.co.discovery.health.journey.util.JourneyHelper.getEnrollmentByCategoryName;
// import static za.co.discovery.health.journey.util.JourneyHelper.getProgramByProgramName;
//
// @Slf4j
// public class JourneyIntegrationTestStepsV2 extends SpringIntegrationTest {
//
//    @Autowired
//    private JourneyProcessor journeyProcessor;
//
//    @Autowired
//    private JourneyActivityService activityService;
//
//    @Autowired
//    private JourneyWorld journeyWorld;
//
//    /**
//     * Call user enrollments as before.
//     */
//    @When("i call user enrollments")
//    public void setUserEnrollments(final DataTable dataTable) {
//        final List<Map<String, String>> maps = dataTable.asMaps(String.class, String.class);
//        final Map<String, String> firstRow = maps.get(0);
//        firstRow.forEach((key, value) -> log.info("Key: {}, Value: {}", key, value));
//        final String alliance = firstRow.get("alliance");
//        final String group = firstRow.get("group");
//        final String branch = firstRow.get("branch");
//        final String role = firstRow.get("role");
//        final LocalDateTime date = Objects.equals(firstRow.get("time"), "now")
//                ? LocalDateTime.now()
//                : LocalDateTime.now().plusDays(Long.parseLong(firstRow.get("time")));
//
//        final List<UserCategoryEnrollmentDto> userEnrollments =
// journeyProcessor.getUserEnrollments(1, date);
//
//        journeyWorld.setUserEnrollments(userEnrollments);
//    }
//
//    /**
//     * Complete an activity using a DataTable with columns like:
//     * <p>
//     * | mnemonic | txn | date |
//     * | GSTR     | 1   | now  |
//     */
//    @When("i complete an activity:")
//    public void completeActivity(final DataTable dataTable) {
//        // We expect one (or more) rows with the keys: mnemonic, txn, date
//        final List<Map<String, String>> maps = dataTable.asMaps(String.class, String.class);
//        for (Map<String, String> row : maps) {
//            final String mnemonic = row.get("mnemonic");
//            final Long txn = Long.parseLong(row.get("txn"));
//            final String date = row.get("date");
//
//            activityService.process(
//                    1L,
//                    mnemonic,
//                    txn,
//                    "now".equals(date)
//                            ? LocalDateTime.now()
//                            : LocalDateTime.now().plusDays(Long.parseLong(date)));
//        }
//    }
//
//    /**
//     * Validate the enrollment list size using a DataTable, e.g.:
//     * <p>
//     * Then i should get user enrollments of size:
//     * | count |
//     * | 2     |
//     */
//    @Then("i should get user enrollments of size:")
//    public void iShouldGetUserEnrollments(final DataTable dataTable) {
//        final List<Map<String, String>> rows = dataTable.asMaps(String.class, String.class);
//        // Each row has a "count" column
//        for (Map<String, String> row : rows) {
//            final int expectedSize = Integer.parseInt(row.get("count"));
//            final List<UserCategoryEnrollmentDto> userEnrollments =
// journeyWorld.getUserEnrollments();
//
//            Assert.assertNotNull("User enrollments list is null", userEnrollments);
//            Assert.assertEquals("Unexpected user enrollments size", expectedSize,
// userEnrollments.size());
//        }
//    }
//
//    /**
//     * Validate a category using a DataTable, e.g.:
//     * <p>
//     * And i should get user enrollments with category:
//     * | category           |
//     * | FITNESS MANAGEMENT |
//     * | Weight Management  |
//     */
//    @And("i should get user enrollments with category:")
//    public void iShouldGetUserEnrollmentsWithCategory(final DataTable dataTable) {
//        final List<Map<String, String>> maps = dataTable.asMaps(String.class, String.class);
//        for (Map<String, String> row : maps) {
//            final String categoryName = row.get("category");
//            final List<UserCategoryEnrollmentDto> userEnrollments =
// journeyWorld.getUserEnrollments();
//            final Optional<UserCategoryEnrollmentDto> userEnrollmentOptional =
//                    getEnrollmentByCategoryName(userEnrollments, categoryName);
//
//            Assert.assertTrue("Category not found: " + categoryName,
// userEnrollmentOptional.isPresent());
//        }
//    }
//
//    /**
//     * Validate program size using a DataTable, e.g.:
//     * <p>
//     * And i should get user enrollments with program size:
//     * | category           | programSize |
//     * | FITNESS MANAGEMENT | 1           |
//     */
//    @And("i should get user enrollments with program size:")
//    public void iShouldGetUserEnrollmentsWithProgramSize(final DataTable dataTable) {
//        final List<Map<String, String>> maps = dataTable.asMaps(String.class, String.class);
//        for (Map<String, String> row : maps) {
//            final String categoryName = row.get("category");
//            final int expectedSize = Integer.parseInt(row.get("programSize"));
//
//            final List<UserCategoryEnrollmentDto> userEnrollments =
// journeyWorld.getUserEnrollments();
//            final Optional<UserCategoryEnrollmentDto> userEnrollmentOptional =
//                    getEnrollmentByCategoryName(userEnrollments, categoryName);
//            Assert.assertTrue("Category not found: " + categoryName,
// userEnrollmentOptional.isPresent());
//
//            final UserCategoryEnrollmentDto userEnrollment = userEnrollmentOptional.get();
//            Assert.assertEquals(
//                    "Mismatch in program size",
//                    expectedSize,
//                    userEnrollment.getJourneyPrograms().size());
//        }
//    }
//
//    /**
//     * Validate a specific program in a category, e.g.:
//     * <p>
//     * Then i should get user enrollments with programs:
//     * | category           | program            | status | order |
//     * | FITNESS MANAGEMENT | Fitness Management | ACTIVE | 1     |
//     */
//    @Then("i should get user enrollments with programs:")
//    public void iShouldGetUserEnrollmentsWithProgram(final DataTable dataTable) {
//        final List<Map<String, String>> maps = dataTable.asMaps(String.class, String.class);
//        for (Map<String, String> row : maps) {
//            final String categoryName = row.get("category");
//            final String programName = row.get("program");
//            final String status = row.get("status");
//            final long order = Long.parseLong(row.get("order"));
//
//            final List<UserCategoryEnrollmentDto> userEnrollments =
// journeyWorld.getUserEnrollments();
//            final Optional<UserCategoryEnrollmentDto> userEnrollmentOptional =
//                    getEnrollmentByCategoryName(userEnrollments, categoryName);
//            Assert.assertTrue("Category not found: " + categoryName,
// userEnrollmentOptional.isPresent());
//
//            final Optional<UserProgramEnrollmentDto> programOptional =
//                    getProgramByProgramName(userEnrollmentOptional.get(), programName);
//            Assert.assertTrue("Program not found: " + programName, programOptional.isPresent());
//
//            final UserProgramEnrollmentDto programDto = programOptional.get();
//            Assert.assertEquals("Unexpected program status", status, programDto.getStatus());
//            if ("ACTIVE".equals(status)) {
//                Assert.assertNull("Program termination date should be null",
// programDto.getTerminationDate());
//            } else {
//                Assert.assertNotNull("Program termination date should not be null",
// programDto.getTerminationDate());
//            }
//            Assert.assertEquals("Program order mismatch", Long.valueOf(order),
// programDto.getOrder());
//        }
//    }
//
//    /**
//     * Validate reward size with a DataTable, e.g.:
//     * <p>
//     * Then i should get user enrollments with programs rewards size:
//     * | category           | program            | milestoneSize |
//     * | FITNESS MANAGEMENT | Fitness Management | 1             |
//     */
//    @Then("i should get user enrollments with programs rewards size:")
//    public void iShouldGetUserEnrollmentsWithProgramRewardSize(final DataTable dataTable) {
//        final List<Map<String, String>> maps = dataTable.asMaps(String.class, String.class);
//        for (Map<String, String> row : maps) {
//            final String categoryName = row.get("category");
//            final String programName = row.get("program");
//            final int rewardSize = Integer.parseInt(row.get("rewardSize"));
//
//            final List<UserCategoryEnrollmentDto> userEnrollments =
// journeyWorld.getUserEnrollments();
//            final Optional<UserCategoryEnrollmentDto> userEnrollmentOptional =
//                    getEnrollmentByCategoryName(userEnrollments, categoryName);
//            Assert.assertTrue("Category not found: " + categoryName,
// userEnrollmentOptional.isPresent());
//
//            final Optional<UserProgramEnrollmentDto> programOptional =
//                    getProgramByProgramName(userEnrollmentOptional.get(), programName);
//            Assert.assertTrue("Program not found: " + programName, programOptional.isPresent());
//
//            final UserProgramEnrollmentDto program = programOptional.get();
//            Assert.assertEquals(
//                    "Mismatch in program reward size",
//                    rewardSize,
//                    program.getRewards().size());
//        }
//    }
//
//    /**
//     * Validate reward details with a DataTable, e.g.:
//     * <p>
//     * Then i should get user enrollments with rewards programs:
//     * | category           | program            | rewardValue | rewardType | awardDate | status |
//     * | FITNESS MANAGEMENT | Fitness Management | 1           | POINTS     | empty     | ACTIVE |
//     */
//    @And("i should get user enrollments with programs rewards:")
//    public void iShouldGetUserEnrollmentsWithProgramWithRewards(final DataTable dataTable) {
//        final List<Map<String, String>> maps = dataTable.asMaps(String.class, String.class);
//        for (Map<String, String> row : maps) {
//            final String categoryName = row.get("category");
//            final String programName = row.get("program");
//            final long rewardValue = Long.parseLong(row.get("rewardValue"));
//            final String rewardType = row.get("rewardType");
//            final String awardDate = row.get("awardDate");
//            final String status = row.get("status");
//
//            final List<UserCategoryEnrollmentDto> userEnrollments =
// journeyWorld.getUserEnrollments();
//            final Optional<UserCategoryEnrollmentDto> userEnrollmentOptional =
//                    getEnrollmentByCategoryName(userEnrollments, categoryName);
//            Assert.assertTrue("Category not found: " + categoryName,
// userEnrollmentOptional.isPresent());
//
//            final Optional<UserProgramEnrollmentDto> programOptional =
//                    getProgramByProgramName(userEnrollmentOptional.get(), programName);
//            Assert.assertTrue("Program not found: " + programName, programOptional.isPresent());
//
//            final UserProgramEnrollmentDto programDto = programOptional.get();
//            Assert.assertNotEquals(
//                    "Rewards list is empty", 0, programDto.getRewards().size());
//
//            programDto.getRewards().forEach(reward -> {
//                Assert.assertEquals("Reward value mismatch", rewardValue,
// reward.getRewardValue());
//                Assert.assertEquals("Reward type mismatch", rewardType, reward.getRewardType());
//                Assert.assertEquals("Award date mismatch", awardDate.equals("empty"),
// reward.getAwardedAt() == null);
//                Assert.assertEquals("Status mismatch", status, reward.getStatus());
//            });
//        }
//    }
//
//    /**
//     * Validate milestone size with a DataTable, e.g.:
//     * <p>
//     * Then i should get user enrollments with milestone sizes:
//     * | category           | program            | milestoneSize |
//     * | FITNESS MANAGEMENT | Fitness Management | 1             |
//     */
//    @Then("i should get user enrollments with milestone sizes:")
//    public void iShouldGetUserEnrollmentsWithMilestone(final DataTable dataTable) {
//        final List<Map<String, String>> maps = dataTable.asMaps(String.class, String.class);
//        for (Map<String, String> row : maps) {
//            final String categoryName = row.get("category");
//            final String programName = row.get("program");
//            final int milestoneSize = Integer.parseInt(row.get("milestoneSize"));
//
//            final List<UserCategoryEnrollmentDto> userEnrollments =
// journeyWorld.getUserEnrollments();
//            final Optional<UserCategoryEnrollmentDto> userEnrollmentOptional =
//                    getEnrollmentByCategoryName(userEnrollments, categoryName);
//            Assert.assertTrue("Category not found: " + categoryName,
// userEnrollmentOptional.isPresent());
//
//            final Optional<UserProgramEnrollmentDto> programOptional =
//                    getProgramByProgramName(userEnrollmentOptional.get(), programName);
//            Assert.assertTrue("Program not found: " + programName, programOptional.isPresent());
//
//            final UserProgramEnrollmentDto program = programOptional.get();
//            Assert.assertEquals(
//                    "Mismatch in milestone size",
//                    milestoneSize,
//                    program.getMilestones().size());
//        }
//    }
//
//    /**
//     * Validate milestone iterations + status, e.g.:
//     * <p>
//     * Then i should get user enrollments with milestone iterations:
//     * | category           | program            | iteration | status |
//     * | FITNESS MANAGEMENT | Fitness Management | 1         | ACTIVE |
//     */
//    @Then("i should get user enrollments with milestone iterations:")
//    public void iShouldGetUserEnrollmentsWithMilestoneIterations(final DataTable dataTable) {
//        final List<Map<String, String>> maps = dataTable.asMaps(String.class, String.class);
//        for (Map<String, String> row : maps) {
//            final String categoryName = row.get("category");
//            final String programName = row.get("program");
//            final int iteration = Integer.parseInt(row.get("iteration"));
//            final String status = row.get("status");
//
//            final List<UserCategoryEnrollmentDto> userEnrollments =
// journeyWorld.getUserEnrollments();
//            final Optional<UserCategoryEnrollmentDto> userEnrollmentOptional =
//                    getEnrollmentByCategoryName(userEnrollments, categoryName);
//            Assert.assertTrue("Category not found: " + categoryName,
// userEnrollmentOptional.isPresent());
//
//            final Optional<UserProgramEnrollmentDto> programOptional =
//                    getProgramByProgramName(userEnrollmentOptional.get(), programName);
//            Assert.assertTrue("Program not found: " + programName, programOptional.isPresent());
//
//            final UserProgramEnrollmentDto program = programOptional.get();
//            final Optional<UserMilestoneEnrollmentDto> milestoneOptional =
// program.getMilestones().stream()
//                    .filter(m -> m.getIteration() == iteration)
//                    .findFirst();
//            Assert.assertTrue("Milestone iteration not found: " + iteration,
// milestoneOptional.isPresent());
//
//            final UserMilestoneEnrollmentDto milestone = milestoneOptional.get();
//            Assert.assertEquals("Milestone status mismatch", status, milestone.getStatus());
//        }
//    }
//
//    /**
//     * Validate milestone iteration activity size, e.g.:
//     * <p>
//     * And I should get user enrollments with milestone iteration activity sizes:
//     * | category           | program            | iteration | activitySize |
//     * | FITNESS MANAGEMENT | Fitness Management | 1         | 4            |
//     */
//    @Then("i should get user enrollments with milestone iteration activity sizes:")
//    public void iShouldGetUserEnrollmentsWithMilestoneIterationWithActivitySize(final DataTable
// dataTable) {
//        final List<Map<String, String>> maps = dataTable.asMaps(String.class, String.class);
//        for (Map<String, String> row : maps) {
//            final String categoryName = row.get("category");
//            final String programName = row.get("program");
//            final int iteration = Integer.parseInt(row.get("iteration"));
//            final int activitySize = Integer.parseInt(row.get("activitySize"));
//
//            final List<UserCategoryEnrollmentDto> userEnrollments =
// journeyWorld.getUserEnrollments();
//            final Optional<UserCategoryEnrollmentDto> userEnrollmentOptional =
//                    getEnrollmentByCategoryName(userEnrollments, categoryName);
//            Assert.assertTrue("Category not found: " + categoryName,
// userEnrollmentOptional.isPresent());
//
//            final Optional<UserProgramEnrollmentDto> programOptional =
//                    getProgramByProgramName(userEnrollmentOptional.get(), programName);
//            Assert.assertTrue("Program not found: " + programName, programOptional.isPresent());
//
//            final UserProgramEnrollmentDto program = programOptional.get();
//            final Optional<UserMilestoneEnrollmentDto> milestoneOptional =
// program.getMilestones().stream()
//                    .filter(m -> m.getIteration() == iteration)
//                    .findFirst();
//            Assert.assertTrue("Milestone iteration not found: " + iteration,
// milestoneOptional.isPresent());
//
//            final UserMilestoneEnrollmentDto milestone = milestoneOptional.get();
//            Assert.assertEquals(
//                    "Activity size mismatch",
//                    activitySize,
//                    milestone.getActivities().size());
//        }
//    }
//
//    /**
//     * Validate milestone iteration reward size, e.g.:
//     * <p>
//     * And I should get user enrollments with milestone iteration reward sizes:
//     * | category           | program            | iteration | rewardSize |
//     * | FITNESS MANAGEMENT | Fitness Management | 1         | 4            |
//     */
//    @Then("i should get user enrollments with milestone iteration reward sizes:")
//    public void iShouldGetUserEnrollmentsWithMilestoneIterationWithRewardSizes(final DataTable
// dataTable) {
//        final List<Map<String, String>> maps = dataTable.asMaps(String.class, String.class);
//        for (Map<String, String> row : maps) {
//            final String categoryName = row.get("category");
//            final String programName = row.get("program");
//            final int iteration = Integer.parseInt(row.get("iteration"));
//            final int rewardSize = Integer.parseInt(row.get("rewardSize"));
//
//            final List<UserCategoryEnrollmentDto> userEnrollments =
// journeyWorld.getUserEnrollments();
//            final Optional<UserCategoryEnrollmentDto> userEnrollmentOptional =
//                    getEnrollmentByCategoryName(userEnrollments, categoryName);
//            Assert.assertTrue("Category not found: " + categoryName,
// userEnrollmentOptional.isPresent());
//
//            final Optional<UserProgramEnrollmentDto> programOptional =
//                    getProgramByProgramName(userEnrollmentOptional.get(), programName);
//            Assert.assertTrue("Program not found: " + programName, programOptional.isPresent());
//
//            final UserProgramEnrollmentDto program = programOptional.get();
//            final Optional<UserMilestoneEnrollmentDto> milestoneOptional =
// program.getMilestones().stream()
//                    .filter(m -> m.getIteration() == iteration)
//                    .findFirst();
//            Assert.assertTrue("Milestone iteration not found: " + iteration,
// milestoneOptional.isPresent());
//
//            final UserMilestoneEnrollmentDto milestone = milestoneOptional.get();
//            Assert.assertEquals(
//                    "Reward size mismatch", rewardSize, milestone.getRewards().size());
//        }
//    }
//
//    /**
//     * Validate milestone iteration activities in detail, e.g.:
//     * <p>
//     * Then i should get user enrollments with milestone iteration activities:
//     * | category           | program            | iteration | activity | status | completionCount
// | amount | isMandatory |
//     */
//    @Then("i should get user enrollments with milestone iteration activities:")
//    public void iShouldGetUserEnrollmentsWithMilestoneIterationActivities(final DataTable
// dataTable) {
//        final List<Map<String, String>> maps = dataTable.asMaps(String.class, String.class);
//
//        for (Map<String, String> row : maps) {
//            final String categoryName = row.get("category");
//            final String programName = row.get("program");
//            final int iteration = Integer.parseInt(row.get("iteration"));
//            final String activityMnemonic = row.get("activity");
//            final String status = row.get("status");
//            final long completionCount = Long.parseLong(row.get("completionCount"));
//            final long amount = Long.parseLong(row.get("amount"));
//            final boolean isMandatory = Boolean.parseBoolean(row.get("isMandatory"));
//
//            final List<UserCategoryEnrollmentDto> userEnrollments =
// journeyWorld.getUserEnrollments();
//            final Optional<UserCategoryEnrollmentDto> userEnrollmentOptional =
//                    getEnrollmentByCategoryName(userEnrollments, categoryName);
//            Assert.assertTrue("Category not found: " + categoryName,
// userEnrollmentOptional.isPresent());
//
//            final Optional<UserProgramEnrollmentDto> programOptional =
//                    getProgramByProgramName(userEnrollmentOptional.get(), programName);
//            Assert.assertTrue("Program not found: " + programName, programOptional.isPresent());
//
//            final UserProgramEnrollmentDto program = programOptional.get();
//            final Optional<UserMilestoneEnrollmentDto> milestoneOptional =
// program.getMilestones().stream()
//                    .filter(m -> m.getIteration() == iteration)
//                    .findFirst();
//            Assert.assertTrue("Milestone iteration not found: " + iteration,
// milestoneOptional.isPresent());
//
//            final UserMilestoneEnrollmentDto milestone = milestoneOptional.get();
//            final Optional<UserMilestoneEnrollmentDto.ActivityDetails> activityOptional =
//                    milestone.getActivities().stream()
//                            .filter(a -> a.getMnemonic().equals(activityMnemonic))
//                            .findFirst();
//            Assert.assertTrue("Activity not found: " + activityMnemonic,
// activityOptional.isPresent());
//
//            final UserMilestoneEnrollmentDto.ActivityDetails activityDetails =
// activityOptional.get();
//            Assert.assertEquals("Status mismatch on activity " + activityDetails, status,
// activityDetails.getStatus());
//            Assert.assertEquals(
//                    "Completion count mismatch on activity " + activityDetails,
//                    completionCount,
//                    activityDetails.getActivityCompletionCount());
//            Assert.assertEquals(
//                    "Amount mismatchon activity " + activityDetails, amount,
// activityDetails.getActivityAmount());
//            Assert.assertEquals(
//                    "isMandatory mismatch on activity " + activityDetails, isMandatory,
// activityDetails.isMandatory());
//        }
//    }
// }
