package za.co.discovery.health.journey.model;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class UserEnrollmentRequest {
    private String category;
    private String program;
    private String status;
    private int order;
    private int programSize;
    private int milestoneSize;
    private int milestoneIterations;

    public UserEnrollmentRequest(
            final String category,
            final String program,
            final String status,
            final int order,
            final int programSize,
            final int milestoneSize,
            final int milestoneIterations) {
        this.category = category;
        this.program = program;
        this.status = status;
        this.order = order;
        this.programSize = programSize;
        this.milestoneSize = milestoneSize;
        this.milestoneIterations = milestoneIterations;
    }

    // Getters and Setters
}
