package za.co.discovery.health.journey.model;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class MilestoneActivityRequest {
    private int iteration;
    private String activity;
    private String program;
    private String category;
    private String status;
    private long completionCount;
    private long amount;
    private boolean isMandatory;

    public MilestoneActivityRequest(
            final int iteration,
            final String activity,
            final String program,
            final String category,
            final String status,
            final long completionCount,
            final long amount,
            final boolean isMandatory) {
        this.iteration = iteration;
        this.activity = activity;
        this.program = program;
        this.category = category;
        this.status = status;
        this.completionCount = completionCount;
        this.amount = amount;
        this.isMandatory = isMandatory;
    }

    // Getters and Setters
}
