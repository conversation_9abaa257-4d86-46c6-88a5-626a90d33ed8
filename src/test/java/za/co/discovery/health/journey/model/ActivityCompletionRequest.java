package za.co.discovery.health.journey.model;

import lombok.Data;

@Data
public class ActivityCompletionRequest {
    private String mnemonic;
    private Long txn;
    private String date;

    public ActivityCompletionRequest(final String mnemonic, final Long txn, final String date) {
        this.mnemonic = mnemonic;
        this.txn = txn;
        this.date = date;
    }

    // Getters and Setters
}
