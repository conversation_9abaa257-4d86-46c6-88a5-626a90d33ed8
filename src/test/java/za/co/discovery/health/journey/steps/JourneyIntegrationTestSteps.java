// package za.co.discovery.health.journey.steps;
//
// import io.cucumber.java.en.And;
// import io.cucumber.java.en.Then;
// import io.cucumber.java.en.When;
// import lombok.extern.slf4j.Slf4j;
// import org.junit.Assert;
// import org.springframework.beans.factory.annotation.Autowired;
// import za.co.discovery.health.journey.SpringIntegrationTest;
// import za.co.discovery.health.journey.model.user.JourneyEnrollmentProgramDto;
// import za.co.discovery.health.journey.model.user.UserCategoryEnrollmentDto;
// import za.co.discovery.health.journey.model.user.UserMilestoneEnrollmentDto;
// import za.co.discovery.health.journey.service.journey.JourneyActivityService;
// import za.co.discovery.health.journey.service.journey.JourneyProcessor;
// import za.co.discovery.health.journey.util.model.Audience;
// import za.co.discovery.health.journey.world.JourneyWorld;
//
// import java.time.LocalDateTime;
// import java.util.List;
// import java.util.Optional;
//
// import static za.co.discovery.health.journey.util.JourneyHelper.getEnrollmentByCategoryName;
// import static za.co.discovery.health.journey.util.JourneyHelper.getProgramByProgramName;
//
// @Slf4j
// public class JourneyIntegrationTestSteps extends SpringIntegrationTest {
//    @Autowired
//    private JourneyProcessor journeyProcessor;
//
//    @Autowired
//    private JourneyActivityService activityService;
//
//    @Autowired
//    private JourneyWorld journeyWorld;
//
//    @When("i call user enrollments")
//    public void setUserEnrollments() {
//        List<UserCategoryEnrollmentDto> userEnrollments =
//                journeyProcessor.getUserEnrollments(1, new Audience("string", "string", "string",
// "string"));
//
//        journeyWorld.setUserEnrollments(userEnrollments);
//    }
//
//    @When("i complete an activity with mnemonic {} with txn {} with date {}")
//    public void completeActivity(String mnemonic, Long txn, String date) {
//        activityService.process(
//                1L,
//                mnemonic,
//                txn,
//                date.equals("now") ? LocalDateTime.now() :
// LocalDateTime.now().plusDays(Long.parseLong(date)));
//    }
//
//    @Then("i should get user enrollments of size {}")
//    public void iShouldGetUserEnrollments(int count) {
//        List<UserCategoryEnrollmentDto> userEnrollments = journeyWorld.getUserEnrollments();
//        Assert.assertNotNull(userEnrollments);
//        Assert.assertEquals(count, userEnrollments.size());
//    }
//
//    @And("i should get user enrollments with category {}")
//    public void iShouldGetUserEnrollmentsWithCategory(String categoryName) {
//        List<UserCategoryEnrollmentDto> userEnrollments = journeyWorld.getUserEnrollments();
//        Optional<UserCategoryEnrollmentDto> userEnrollmentOptional =
//                getEnrollmentByCategoryName(userEnrollments, categoryName);
//        Assert.assertTrue(userEnrollmentOptional.isPresent());
//    }
//
//    @And("i should get user enrollments with program size {} in category {}")
//    public void iShouldGetUserEnrollmentsWithProgramSize(int programSize, String categoryName) {
//        List<UserCategoryEnrollmentDto> userEnrollments = journeyWorld.getUserEnrollments();
//        Optional<UserCategoryEnrollmentDto> userEnrollmentOptional =
//                getEnrollmentByCategoryName(userEnrollments, categoryName);
//        Assert.assertTrue(userEnrollmentOptional.isPresent());
//        UserCategoryEnrollmentDto userEnrollment = userEnrollmentOptional.get();
//        Assert.assertEquals(programSize, userEnrollment.getJourneyPrograms().size());
//    }
//
//    @Then("i should get user enrollments with program {} in category {} with status {} and order
// {}")
//    public void iShouldGetUserEnrollmentsWithProgram(
//            String programName, String categoryName, String status, int order) {
//        List<UserCategoryEnrollmentDto> userEnrollments = journeyWorld.getUserEnrollments();
//        Optional<UserCategoryEnrollmentDto> userEnrollmentOptional =
//                getEnrollmentByCategoryName(userEnrollments, categoryName);
//        Assert.assertTrue(userEnrollmentOptional.isPresent());
//
//        Optional<JourneyEnrollmentProgramDto> programOptional =
//                getProgramByProgramName(userEnrollmentOptional.get(), programName);
//        Assert.assertTrue(programOptional.isPresent());
//
//        JourneyEnrollmentProgramDto programDto = programOptional.get();
//
//        Assert.assertEquals(status, programDto.getStatus());
//        Assert.assertNull(programDto.getTerminationDate());
//        Assert.assertEquals(Long.valueOf(order), programDto.getOrder());
//    }
//
//    @Then("i should get user enrollments with milestone size {} in program {} in category {}")
//    public void iShouldGetUserEnrollmentsWithMilestone(int milestoneSize, String programName,
// String categoryName) {
//        List<UserCategoryEnrollmentDto> userEnrollments = journeyWorld.getUserEnrollments();
//        Optional<UserCategoryEnrollmentDto> userEnrollmentOptional =
//                getEnrollmentByCategoryName(userEnrollments, categoryName);
//        Assert.assertTrue(userEnrollmentOptional.isPresent());
//
//        Optional<JourneyEnrollmentProgramDto> programOptional =
//                getProgramByProgramName(userEnrollmentOptional.get(), programName);
//        Assert.assertTrue(programOptional.isPresent());
//
//        JourneyEnrollmentProgramDto program = programOptional.get();
//
//        Assert.assertEquals(milestoneSize, program.getMilestones().size());
//    }
//
//    @Then(
//            "i should get user enrollments with milestone iterations {} in program {} in category
// {} should be of status {}")
//    public void iShouldGetUserEnrollmentsWithMilestoneIterations(
//            int iteration, String programName, String categoryName, String status) {
//        List<UserCategoryEnrollmentDto> userEnrollments = journeyWorld.getUserEnrollments();
//        Optional<UserCategoryEnrollmentDto> userEnrollmentOptional =
//                getEnrollmentByCategoryName(userEnrollments, categoryName);
//        Assert.assertTrue(userEnrollmentOptional.isPresent());
//
//        Optional<JourneyEnrollmentProgramDto> programOptional =
//                getProgramByProgramName(userEnrollmentOptional.get(), programName);
//        Assert.assertTrue(programOptional.isPresent());
//
//        JourneyEnrollmentProgramDto program = programOptional.get();
//        Optional<UserMilestoneEnrollmentDto> milestoneOptional = program.getMilestones().stream()
//                .filter(milestone -> milestone.getIteration() == iteration)
//                .findFirst();
//        Assert.assertTrue(milestoneOptional.isPresent());
//        UserMilestoneEnrollmentDto milestone = milestoneOptional.get();
//        Assert.assertEquals(status, milestone.getStatus());
//    }
//
//    @Then(
//            "i should get user enrollments with milestone iteration {} with activity size {} in
// program {} in category {}")
//    public void iShouldGetUserEnrollmentsWithMilestoneIterationWithActivitySize(
//            int iteration, int activitySize, String programName, String categoryName) {
//        List<UserCategoryEnrollmentDto> userEnrollments = journeyWorld.getUserEnrollments();
//        Optional<UserCategoryEnrollmentDto> userEnrollmentOptional =
//                getEnrollmentByCategoryName(userEnrollments, categoryName);
//        Assert.assertTrue(userEnrollmentOptional.isPresent());
//
//        Optional<JourneyEnrollmentProgramDto> programOptional =
//                getProgramByProgramName(userEnrollmentOptional.get(), programName);
//        Assert.assertTrue(programOptional.isPresent());
//
//        JourneyEnrollmentProgramDto program = programOptional.get();
//        Optional<UserMilestoneEnrollmentDto> milestoneOptional = program.getMilestones().stream()
//                .filter(milestone -> milestone.getIteration() == iteration)
//                .findFirst();
//        Assert.assertTrue(milestoneOptional.isPresent());
//        UserMilestoneEnrollmentDto milestone = milestoneOptional.get();
//        Assert.assertEquals(activitySize, milestone.getActivities().size());
//    }
//
//    @Then(
//            "i should get user enrollments with milestone iteration {} with activity {} in program
// {} in category {} should be of status {} and completion count {} and amount {} and isMandatory
// {}")
//    public void iShouldGetUserEnrollmentsWithMilestoneIterationWithActivity(
//            int iteration,
//            String activity,
//            String programName,
//            String categoryName,
//            String status,
//            long completionCount,
//            long amount,
//            boolean isMandatory) {
//        List<UserCategoryEnrollmentDto> userEnrollments = journeyWorld.getUserEnrollments();
//        Optional<UserCategoryEnrollmentDto> userEnrollmentOptional =
//                getEnrollmentByCategoryName(userEnrollments, categoryName);
//        Assert.assertTrue(userEnrollmentOptional.isPresent());
//
//        Optional<JourneyEnrollmentProgramDto> programOptional =
//                getProgramByProgramName(userEnrollmentOptional.get(), programName);
//        Assert.assertTrue(programOptional.isPresent());
//
//        JourneyEnrollmentProgramDto program = programOptional.get();
//        Optional<UserMilestoneEnrollmentDto> milestoneOptional = program.getMilestones().stream()
//                .filter(milestone -> milestone.getIteration() == iteration)
//                .findFirst();
//        Assert.assertTrue(milestoneOptional.isPresent());
//        UserMilestoneEnrollmentDto milestone = milestoneOptional.get();
//        Optional<UserMilestoneEnrollmentDto.ActivityDetails> activityOptional =
// milestone.getActivities().stream()
//                .filter(activityDetails -> activityDetails.getMnemonic().equals(activity))
//                .findFirst();
//        Assert.assertTrue(activityOptional.isPresent());
//        UserMilestoneEnrollmentDto.ActivityDetails activityDetails = activityOptional.get();
//        Assert.assertEquals(status, activityDetails.getStatus());
//
//        Assert.assertEquals(completionCount, activityDetails.getActivityCompletionCount());
//        Assert.assertEquals(amount, activityDetails.getActivityAmount());
//
//        Assert.assertEquals(isMandatory, activityDetails.isMandatory());
//    }
// }
