# Spring Batch Migration Plan

## Overview
Migration from custom import orchestrator to Spring Batch framework for journey data processing.

**Current Status**: Phase 3 (Normalization Step) fully completed with comprehensive testing. Ready to begin Phase 4 (Enrichment Step) implementation.

## Phase Progress Tracker

### ✅ Phase 1: Spring Batch Setup (COMPLETED)
- Spring Batch dependencies added
- Basic job configuration created
- Database schema for Spring Batch tables

### ✅ Phase 2: Staging Step Migration (COMPLETED)
**Testing Pattern**: Uses custom job approach for comprehensive step testing
- **Package Structure**: Created `batch.stage` package for staging components
- **StagingItemProcessor**: Processes ParsedCsvRow to StagingRecord
- **StagingItemWriter**: Saves staging records and creates bridge table entries
- **CsvFileItemReader**: Reads CSV files with error handling
- **ParsedCsvRowLineMapper**: Maps CSV lines to ParsedCsvRow with error capture
- **Bridge Table Integration**: Links staging records to Spring Batch step executions
- **Comprehensive Testing**: Unit tests covering valid data, invalid rows, empty files, corrupted content
- **ISSUE RESOLVED**: Foreign key constraint violation fixed by removing FK_STAGING_BATCH_STEP constraint
- **ISSUE RESOLVED**: CSV parsing errors now captured in ParsedCsvRow instead of failing step

### ✅ Phase 3: Normalization Step Migration (COMPLETED)
**Testing Pattern**: Uses custom job approach with staging + normalization steps
**Status**: Fully completed with comprehensive error scenario testing
**Goal**: Replace custom normalization logic with Spring Batch step
**Package**: `batch.normalize`

#### ✅ Phase 3.1: Create Normalize Package Structure (COMPLETED)
- ✅ Created `batch.normalize` package following staging pattern
- ✅ Created NormalizeStepParameters for step configuration
- ✅ Created NormalizeStepException for step-specific errors

#### ✅ Phase 3.2: Normalization ItemReader (COMPLETED)
- ✅ Created StagingRecordItemReader extending RepositoryItemReader
- ✅ Filters records by status (LOADED) and job execution context
- ✅ Implements dynamic argument setting via @BeforeStep
- ✅ Uses StagingToEntityCrossmap for job association

#### ✅ Phase 3.3: Normalization ItemProcessor (COMPLETED)
- ✅ Created NormalizationItemProcessor as pass-through filter
- ✅ Skips non-LOADED records for processing
- ✅ Simplified design - business logic moved to writer
- ✅ Uses DppCsvHeader constants for JSON property access

#### ✅ Phase 3.4: Normalization ItemWriter (COMPLETED)
- ✅ Created NormalizationItemWriter using NormalizationDataService
- ✅ Saves normalized entities: Member and crossmap entries
- ✅ Updates StagingRecord status to NORMALIZED
- ✅ Error handling via NormalizationSkipListener

#### ✅ Phase 3.5: Update Job Configuration (COMPLETED)
- ✅ Replaced normalize tasklet with proper step in BatchConfig
- ✅ Configured step with fault tolerance and skip policy
- ✅ Added NormalizationSkipListener for error handling
- ✅ Set up chunk size and transaction management

#### ✅ Phase 3.6: Error Scenario Testing (COMPLETED)
- ✅ Basic success test: testNormalizationStepWithValidStagingRecords
- ✅ Test invalid JSON payload: testNormalizationStepWithInvalidJsonPayload
- ✅ Test missing required fields: testNormalizationStepWithMissingRequiredFields
- ✅ Test partial batch failures: testNormalizationStepWithMixedValidInvalidRecords
- ✅ Skip listener error capture and logging verified
- ✅ Step execution statistics validation (read/write/skip counts)
- ✅ Error record creation and StagingRecordError tracking
- ✅ Member entity creation and crossmap entry validation

### ⏳ Phase 4: Enrichment Step Migration
**Status**: Ready to Start - All prerequisites from Phase 3 completed
**Goal**: Replace custom enrichment logic with Spring Batch step
**Package**: `batch.enrich`

#### ✅ Phase 4.1: Create Enrich Package Structure (COMPLETED)
- ✅ Create `batch.enrich` package following established pattern
- ✅ Create EnrichStepParameters for step configuration
- ✅ Create EnrichStepException for step-specific errors

#### ✅ Phase 4.2: Enrichment ItemReader (COMPLETED)
- ✅ Create EnrichmentItemReader to read normalized Member entities
- ✅ Filter by members needing enrichment (entity_no is null)
- ✅ Handle member entity relationships via StagingToEntityCrossmap

#### ✅ Phase 4.3: Entity Service Integration (COMPLETED)
- ✅ Create EnrichmentItemProcessor for member entity number lookup
- ✅ Integrate with EntityNoResolver for external service calls
- ✅ Handle service failures with EnrichStepException
- ✅ Return enriched member with updated entityNo

#### ✅ Phase 4.4: Enrichment ItemWriter (COMPLETED)
- ✅ Create EnrichmentItemWriter to update Member entities with entity_no
- ✅ Simple writer that saves enriched Member entities
- ✅ Integrated with existing crossmap structure
- ✅ Logging for successful enrichment operations

#### ✅ Phase 4.5: Error Handling & Job Configuration (COMPLETED)
- ✅ Implement EnrichmentSkipListener for entity service failures
- ✅ Create error records in associated staging records
- ✅ Update BatchConfig with enrichment step configuration
- ✅ Add fault tolerance and skip policies
- ✅ Create comprehensive test suite for enrichment step

### ⏳ Phase 5: Enrollment Step Migration
**Status**: In Progress
**Goal**: Replace journey enrollment trigger with Spring Batch step
**Package**: `batch.enrollment`

#### ✅ Phase 5.1: Create Enrollment Package Structure (COMPLETED)
- ✅ Create `batch.enrollment` package following established pattern
- ✅ Create EnrollmentStepParameters for step configuration
- ✅ Create EnrollmentStepException for step-specific errors
- ✅ Add ENROLLMENT step to ImportJobSteps enum
- ✅ Update BatchConfig with enrollmentStep bean

#### ✅ Phase 5.2: Enrollment ItemReader (COMPLETED - Placeholder)
- ✅ Create EnrollmentItemReader to read enriched members
- ✅ Use existing MemberRepositoryExtension.findByJobId method
- ✅ Filter by job execution context

#### ✅ Phase 5.3: Journey Service Integration (COMPLETED - Placeholder)
- ✅ Create EnrollmentItemProcessor for triggering enrollments
- ✅ Placeholder implementation with TODO for external API calls
- ✅ Error handling structure in place

#### ✅ Phase 5.4: Enrollment ItemWriter (COMPLETED - Placeholder)
- ✅ Create EnrollmentItemWriter for enrollment completion tracking
- ✅ Placeholder implementation with TODO for completion tracking
- ✅ Logging structure in place

#### ✅ Phase 5.5: Error Handling & Job Configuration (COMPLETED - Placeholder)
- ✅ Create EnrollmentSkipListener for error handling
- ✅ Placeholder implementation with TODO for error tracking
- ✅ Update job pipeline: staging → normalize → enrich → enrollment
- ✅ Configure fault tolerance and skip policies

### ⏳ Phase 6: Testing & Validation
**Status**: Partially Complete
**Goal**: Ensure Spring Batch implementation matches existing functionality

#### Phase 6.1: Integration Test Updates
- [ ] Update ImportProcessIntegrationTest to use Spring Batch jobs
- [ ] Replace ImportOrchestrator calls with JobLauncher
- [ ] Verify all existing test scenarios pass

#### Phase 6.2: Performance Testing
- [ ] Compare performance between custom and Spring Batch implementations
- [ ] Optimize chunk sizes and commit intervals
- [ ] Validate memory usage with large datasets

#### Phase 6.3: Error Scenario Testing
- [ ] Test all error scenarios (file parsing, validation, service failures)
- [ ] Verify error handling and recovery mechanisms
- [ ] Ensure proper rollback behavior

### ⏳ Phase 7: Cutover & Cleanup
**Status**: Not Started
**Goal**: Switch to Spring Batch and remove legacy code

#### Phase 7.1: Configuration Switch
- [ ] Enable Spring Batch job auto-start
- [ ] Update REST endpoints to use JobLauncher
- [ ] Disable legacy import orchestrator

#### Phase 7.2: Legacy Code Removal
- [ ] Remove custom step implementations
- [ ] Clean up unused imports and dependencies
- [ ] Update documentation

#### Phase 7.3: Monitoring Setup
- [ ] Configure Spring Batch Admin/monitoring

## Package Structure Guidelines

### Established Pattern
```
batch/
├── BatchConfig.java              # Main job configuration
├── BatchException.java           # Base batch exception
├── BatchStepParameters.java      # Common step parameters interface
├── ImportJobs.java              # Job constants/utilities
├── ImportJobSteps.java          # Step constants/utilities
└── {step}/                      # Step-specific package
    ├── {Step}ItemReader.java    # Reads data for step
    ├── {Step}ItemProcessor.java # Processes data for step
    ├── {Step}ItemWriter.java    # Writes data for step
    ├── {Step}StepParameters.java # Step parameters builder
    └── {Step}StepException.java # Step-specific exceptions
```

### Rules
- **Spring Batch code**: Place in `batch` package with step-specific subpackages
- **Business logic**: Keep in `service` and domain packages
- **Each step**: Gets its own package (stage, normalize, enrich, enrollment)
- **Consistent naming**: Follow {Step}Item{Reader|Processor|Writer} pattern

## Current Issues & Blockers

### ✅ Resolved Issues
1. **Foreign Key Constraint Violation**: BATCH_STEP_EXECUTION_ID constraint removed via migration *********.1000.3
2. **CSV Parsing Errors**: ParsedCsvRowLineMapper now catches parsing exceptions and returns error records
3. **Package Structure**: Reorganized into step-specific packages following established pattern
4. **Bridge Table Migration**: Replaced StagingBatchBridge with StagingToEntityCrossmap for job association
5. **Test Isolation Issues**: Fixed by disabling automatic batch job startup in test configuration
6. **Spring Batch Error Handling**: Implemented skip listeners instead of nested transactions
7. **Parameter Inheritance**: Job parameters automatically inherited by steps via stepExecution.getJobParameters()
8. **Repository Lifecycle**: RepositoryItemReader requires constructor initialization with dynamic arguments in @BeforeStep
9. **Test Context Pollution**: Resolved by using single job with tasklet steps for data corruption between staging and normalization
10. **DppCsvHeader Constants**: Updated all tests to use DppCsvHeader constants instead of hardcoded JSON stringstch job startup in test configuration
6. **Spring Batch Error Handling**: Implemented skip listeners instead of nested transactions
7. **Parameter Inheritance**: Job parameters automatically inherited by steps via stepExecution.getJobParameters()
8. **Repository Lifecycle**: RepositoryItemReader requires constructor initialization with dynamic arguments in @BeforeStep

## Next Steps
1. Begin Phase 4.1: Create `batch.enrich` package structure for enrichment step
2. Implement EnrichmentItemReader to read normalized Member entities
3. Continue following established patterns from staging and normalization implementations

## Key Learnings
- Spring Batch manages its own transaction lifecycle - avoid foreign key constraints to Spring Batch tables
- Bridge tables should track relationships without database enforcement for framework integration
- Error handling should happen at LineMapper level to capture parsing errors as data rather than exceptions
- Step-specific packages improve code organization and maintainability
- Consistent naming patterns improve code readability and maintenance
- Skip listeners provide cleaner error handling than nested transactions for Spring Batch
- Custom job pattern using JobBuilder ensures proper job execution context between steps
- RepositoryItemReader requires initialization in constructor before Spring calls afterPropertiesSet()
- Job parameters are automatically inherited by all steps, accessible via stepExecution.getJobParameters()
- Test configuration must disable automatic batch job startup to prevent context pollution
- StagingToEntityCrossmap provides flexible entity relationship tracking without database constraints
- Custom tasklet steps between main processing steps enable controlled data corruption for error testing
- TestFile helper class with AutoCloseable provides elegant temporary file cleanup
- Dedicated parameter builders (StageStepParameters, NormalizeStepParameters) improve test maintainability

## Testing Best Practices

### Custom Job Pattern
For Spring Batch step testing, use custom job pattern to ensure proper job execution context:

```java
// Create custom job with required steps
Job testJob = new JobBuilder("testJobName", jobRepository)
    .start(stagingStep)
    .next(normalizeStep)
    .build();

// Set the test job and execute
jobLauncherTestUtils.setJob(testJob);
JobExecution jobExecution = jobLauncherTestUtils.launchJob(jobParams);
```

### Benefits
- Ensures proper StagingBatchBridge relationships between steps
- Provides realistic job execution context
- Allows testing of step interactions and parameter passing
- Validates complete job flow scenarios

### Pattern Implementation
- Each test creates unique Job with new JobBuilder("testJobName", jobRepository)
- Inject Step and JobRepository dependencies into test class
- Set job on jobLauncherTestUtils before execution
- Verify step execution counts and statistics (read/write/skip counts)
- Use custom tasklet steps for data manipulation between main processing steps
- Validate error capture via StagingRecordError entities and skip listeners

### Error Testing Patterns
- **Single Job Approach**: Use staging + tasklet + normalization steps in one job
- **Tasklet Data Corruption**: Custom tasklets between steps to corrupt data for error scenarios
- **Comprehensive Validation**: Verify step statistics, error records, and entity creation
- **DppCsvHeader Constants**: Use constants for JSON property names in test data the codebase more predictable and easier to navigate

## Testing Best Practices

### Custom Job Pattern for Step Testing
When testing Spring Batch steps, create custom jobs with only the steps you need to test:

```java
// Create custom job with specific steps
Job testJob = new JobBuilder("testJobName", jobRepository)
    .start(stepToTest)
    .next(dependentStep)  // Only if needed
    .build();

// Set custom job and execute
jobLauncherTestUtils.setJob(testJob);
JobExecution jobExecution = jobLauncherTestUtils.launchJob(jobParams);
```

**Benefits:**
- Ensures proper job execution context between steps
- Tests only the steps you need (avoids unnecessary step executions)
- Maintains Spring Batch transaction and job parameter inheritance
- Allows verification of step execution counts and order

**Use this pattern for all future step testing instead of testing individual steps in isolation.**